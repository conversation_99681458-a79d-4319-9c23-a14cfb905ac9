import { flatRoutes } from "remix-flat-routes";

/** @type {import('@remix-run/dev').AppConfig} */
export default {
  appDirectory: "app",
  assetsBuildDirectory: "public/build",
  postcss: true,
  publicPath: "/build/",
  serverBuildPath: "build/index.js",
  serverDependenciesToBundle: ["react-audio-voice-recorder", /date-fns*/],
  serverModuleFormat: "esm",
  tailwind: true,
  routes(defineRoutes) {
    return flatRoutes("routes", defineRoutes, {
      ignoredRouteFiles: ["**/.*"],
      routeRegex: /^api\/(.+)/, // Treat api/* as nested routes
    });
  },
};
