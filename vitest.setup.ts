import "@testing-library/jest-dom";
import "@testing-library/jest-dom/vitest";
import * as matchers from "@testing-library/jest-dom/matchers";

// Mock matchMedia and ResizeObserver
beforeAll(() => {
  // Only define this in the browser testing environment, not the node environment.
  if (typeof window === "undefined") {
    return;
  }

  Object.defineProperty(window, "matchMedia", {
    writable: true,
    value: vi.fn().mockImplementation((query: string) => {
      return {
        matches: false,
        media: query,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };
    }),
  });

  // Mock ResizeObserver
  if (!global.ResizeObserver) {
    global.ResizeObserver = class ResizeObserver {
      observe() {}
      unobserve() {}
      disconnect() {}
    };
  }
});

// Extend Vitest's expect with jest-dom matchers
expect.extend(matchers);

// Add custom project-specific matchers.
expect.extend({
  toBeHidden(received: HTMLElement) {
    return {
      pass: received.closest("[hidden]") !== null,
      message: () => "Expected element to be hidden",
    };
  },
});
