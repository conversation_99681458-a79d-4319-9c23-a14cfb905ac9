import { describe, it, expect } from "vitest";
import { render, screen } from "@testing-library/react";
import { SettingsBanner } from "./SettingsBanner";

describe("SettingsBanner", () => {
  it("displays full name when both firstName and lastName provided", () => {
    render(
      <SettingsBanner
        firstName="John"
        lastName="Doe"
        email="<EMAIL>"
      />
    );
    expect(screen.getByText("John Doe")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
  });

  it("displays only firstName when lastName is null", () => {
    render(
      <SettingsBanner
        firstName="John"
        lastName={null}
        email="<EMAIL>"
      />
    );
    expect(screen.getByText("John")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
  });

  it("displays only lastName when firstName is null", () => {
    render(
      <SettingsBanner
        firstName={null}
        lastName="Doe"
        email="<EMAIL>"
      />
    );
    expect(screen.getByText("Doe")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
  });

  it("displays 'Zeplyn User' when both firstName and lastName are null", () => {
    render(
      <SettingsBanner
        firstName={null}
        lastName={null}
        email="<EMAIL>"
      />
    );
    expect(screen.getByText("Zeplyn User")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
  });

  it("displays 'Zeplyn User' when both firstName and lastName are undefined", () => {
    render(
      <SettingsBanner
        firstName={undefined}
        lastName={undefined}
        email="<EMAIL>"
      />
    );
    expect(screen.getByText("Zeplyn User")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
  });
});
