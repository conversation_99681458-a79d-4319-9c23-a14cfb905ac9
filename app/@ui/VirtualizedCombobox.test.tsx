import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { VirtualizedCombobox } from "./VirtualizedCombobox";

// Mock react-window to avoid complex virtualization testing
vi.mock("react-window", () => ({
  FixedSizeList: ({ children, itemCount }: any) => {
    const items = [];
    for (let i = 0; i < itemCount; i++) {
      items.push(
        <div key={i}>
          {children({
            index: i,
            style: { height: 40 },
          })}
        </div>
      );
    }
    return <div data-testid="virtualized-list">{items}</div>;
  },
}));

// Mock the Command components to avoid complex UI interactions
vi.mock("~/@shadcn/ui/command", () => ({
  Command: ({ children }: any) => <div data-testid="command">{children}</div>,
  CommandInput: ({ placeholder, value, onValueChange }: any) => (
    <input
      data-testid="command-input"
      placeholder={placeholder}
      value={value}
      onChange={(e) => onValueChange?.(e.target.value)}
    />
  ),
  CommandEmpty: ({ children }: any) => <div data-testid="command-empty">{children}</div>,
  CommandGroup: ({ children }: any) => <div data-testid="command-group">{children}</div>,
  CommandItem: ({ children, onSelect, value }: any) => (
    <div
      data-testid="command-item"
      onClick={() => onSelect?.(value)}
      role="option"
      aria-selected="false"
    >
      {children}
    </div>
  ),
  CommandList: ({ children }: any) => <div data-testid="command-list">{children}</div>,
}));

// Mock Popover components
vi.mock("~/@shadcn/ui/popover", () => ({
  Popover: ({ children, open }: any) => (
    <div data-testid="popover" data-open={open}>
      {children}
    </div>
  ),
  PopoverTrigger: ({ children }: any) => children,
  PopoverContent: ({ children }: any) => (
    <div data-testid="popover-content">{children}</div>
  ),
}));

describe("VirtualizedCombobox", () => {
  const mockOptions = [
    { value: "1", label: "Option 1", leftIcon: <span>🔥</span> },
    { value: "2", label: "Option 2" },
    { value: "3", label: "Option 3" },
  ];

  const mockLoadOptions = vi.fn();
  const mockOnChange = vi.fn();

  const defaultProps = {
    options: mockOptions,
    onChange: mockOnChange,
    loadOptions: mockLoadOptions,
  };

  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.clearAllMocks();
    vi.restoreAllMocks();
  });

  it("renders with placeholder text", () => {
    render(
      <VirtualizedCombobox
        {...defaultProps}
        placeholder="Select an option"
      />
    );

    expect(screen.getByRole("combobox")).toBeInTheDocument();
    expect(screen.getByText("Select an option")).toBeInTheDocument();
  });

  it("renders with selected object", () => {
    const selectedObject = { value: "1", label: "Option 1" };
    render(
      <VirtualizedCombobox
        {...defaultProps}
        selectedObject={selectedObject}
      />
    );

    // Check that the button shows the selected option
    const button = screen.getByRole("combobox");
    expect(button).toBeInTheDocument();
    expect(screen.getAllByText("Option 1")).toHaveLength(2); // One in button, one in dropdown
  });

  it("renders disabled state correctly", () => {
    render(<VirtualizedCombobox {...defaultProps} disabled />);

    const button = screen.getByRole("combobox");
    expect(button).toBeDisabled();
  });

  it("renders left icon when provided", () => {
    render(
      <VirtualizedCombobox
        {...defaultProps}
        leftIcon={<span data-testid="left-icon">🔍</span>}
      />
    );

    expect(screen.getByTestId("left-icon")).toBeInTheDocument();
  });

  it("applies custom className props", () => {
    render(
      <VirtualizedCombobox
        {...defaultProps}
        triggerClassName="custom-trigger"
        commandClassName="custom-command"
      />
    );

    const button = screen.getByRole("combobox");
    expect(button).toHaveClass("custom-trigger");
  });

  it("renders with correct props structure", () => {
    render(<VirtualizedCombobox {...defaultProps} />);

    // Test that the component renders without crashing
    expect(screen.getByRole("combobox")).toBeInTheDocument();
    expect(screen.getByTestId("popover")).toBeInTheDocument();
  });

  it("handles empty options array", () => {
    render(
      <VirtualizedCombobox
        {...defaultProps}
        options={[]}
      />
    );

    expect(screen.getByRole("combobox")).toBeInTheDocument();
  });

  it("renders with custom item size and max height", () => {
    render(
      <VirtualizedCombobox
        {...defaultProps}
        itemSize={50}
        maxHeightPx={400}
      />
    );

    expect(screen.getByRole("combobox")).toBeInTheDocument();
  });
});