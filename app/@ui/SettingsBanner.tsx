import { PersonOutlineOutlined } from "@mui/icons-material";
import { Avatar, AvatarFallback } from "~/@shadcn/ui/avatar";

/// Returns a name to display in the settings banner.
const nameForDisplay = (
  firstName: string | null | undefined,
  lastName: string | null | undefined
) => {
  if (firstName && lastName) {
    return `${firstName} ${lastName}`;
  } else if (firstName) {
    return firstName;
  } else if (lastName) {
    return lastName;
  } else {
    return "Zeplyn User";
  }
};

// Exports
type Props = {
  firstName: string | null | undefined;
  lastName: string | null | undefined;
  email: string;
};
export const SettingsBanner = ({ firstName, lastName, email }: Props) => (
  <div className="flex items-center gap-8 px-0 py-8">
    <Avatar className="h-20 w-20">
      <AvatarFallback>
        <PersonOutlineOutlined className="h-2/3 w-2/3" />
      </AvatarFallback>
    </Avatar>
    <div className="flex w-full flex-grow flex-col">
      <h1 className="hyphens-auto break-all text-2xl font-semibold">
        {nameForDisplay(firstName, lastName)}
      </h1>
      <p className="hyphens-auto break-all text-secondary">{email}</p>
    </div>
  </div>
);
