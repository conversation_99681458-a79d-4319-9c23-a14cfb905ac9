/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { LinkedCRMEntity } from './LinkedCRMEntity';
import {
    LinkedCRMEntityFromJSON,
    LinkedCRMEntityFromJSONTyped,
    LinkedCRMEntityToJSON,
} from './LinkedCRMEntity';
import type { EventParticipant } from './EventParticipant';
import {
    EventParticipantFromJSON,
    EventParticipantFromJSONTyped,
    EventParticipantToJSON,
} from './EventParticipant';

/**
 * 
 * @export
 * @interface ScheduledEvent
 */
export interface ScheduledEvent {
    /**
     * 
     * @type {string}
     * @memberof ScheduledEvent
     */
    provider: string;
    /**
     * 
     * @type {string}
     * @memberof ScheduledEvent
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof ScheduledEvent
     */
    userSpecificId: string;
    /**
     * 
     * @type {string}
     * @memberof ScheduledEvent
     */
    title: string;
    /**
     * 
     * @type {Date}
     * @memberof ScheduledEvent
     */
    startTime: Date;
    /**
     * 
     * @type {Date}
     * @memberof ScheduledEvent
     */
    endTime: Date;
    /**
     * 
     * @type {boolean}
     * @memberof ScheduledEvent
     */
    allDay: boolean;
    /**
     * 
     * @type {Array<EventParticipant>}
     * @memberof ScheduledEvent
     */
    participants: Array<EventParticipant>;
    /**
     * 
     * @type {Array<string>}
     * @memberof ScheduledEvent
     */
    meetingUrls: Array<string>;
    /**
     * 
     * @type {LinkedCRMEntity}
     * @memberof ScheduledEvent
     */
    linkedCrmEntity?: LinkedCRMEntity | null;
    /**
     * 
     * @type {string}
     * @memberof ScheduledEvent
     */
    scheduledEventUuid: string;
    /**
     * 
     * @type {boolean}
     * @memberof ScheduledEvent
     */
    autojoinAvailable: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof ScheduledEvent
     */
    autojoinEnabled: boolean;
}

/**
 * Check if a given object implements the ScheduledEvent interface.
 */
export function instanceOfScheduledEvent(value: object): value is ScheduledEvent {
    if (!('provider' in value) || value['provider'] === undefined) return false;
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('userSpecificId' in value) || value['userSpecificId'] === undefined) return false;
    if (!('title' in value) || value['title'] === undefined) return false;
    if (!('startTime' in value) || value['startTime'] === undefined) return false;
    if (!('endTime' in value) || value['endTime'] === undefined) return false;
    if (!('allDay' in value) || value['allDay'] === undefined) return false;
    if (!('participants' in value) || value['participants'] === undefined) return false;
    if (!('meetingUrls' in value) || value['meetingUrls'] === undefined) return false;
    if (!('scheduledEventUuid' in value) || value['scheduledEventUuid'] === undefined) return false;
    if (!('autojoinAvailable' in value) || value['autojoinAvailable'] === undefined) return false;
    if (!('autojoinEnabled' in value) || value['autojoinEnabled'] === undefined) return false;
    return true;
}

export function ScheduledEventFromJSON(json: any): ScheduledEvent {
    return ScheduledEventFromJSONTyped(json, false);
}

export function ScheduledEventFromJSONTyped(json: any, ignoreDiscriminator: boolean): ScheduledEvent {
    if (json == null) {
        return json;
    }
    return {
        
        'provider': json['provider'],
        'id': json['id'],
        'userSpecificId': json['user_specific_id'],
        'title': json['title'],
        'startTime': (new Date(json['start_time'])),
        'endTime': (new Date(json['end_time'])),
        'allDay': json['all_day'],
        'participants': ((json['participants'] as Array<any>).map(EventParticipantFromJSON)),
        'meetingUrls': json['meeting_urls'],
        'linkedCrmEntity': json['linked_crm_entity'] == null ? undefined : LinkedCRMEntityFromJSON(json['linked_crm_entity']),
        'scheduledEventUuid': json['scheduled_event_uuid'],
        'autojoinAvailable': json['autojoin_available'],
        'autojoinEnabled': json['autojoin_enabled'],
    };
}

export function ScheduledEventToJSON(value?: ScheduledEvent | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'provider': value['provider'],
        'id': value['id'],
        'user_specific_id': value['userSpecificId'],
        'title': value['title'],
        'start_time': ((value['startTime']).toISOString()),
        'end_time': ((value['endTime']).toISOString()),
        'all_day': value['allDay'],
        'participants': ((value['participants'] as Array<any>).map(EventParticipantToJSON)),
        'meeting_urls': value['meetingUrls'],
        'linked_crm_entity': LinkedCRMEntityToJSON(value['linkedCrmEntity']),
        'scheduled_event_uuid': value['scheduledEventUuid'],
        'autojoin_available': value['autojoinAvailable'],
        'autojoin_enabled': value['autojoinEnabled'],
    };
}

