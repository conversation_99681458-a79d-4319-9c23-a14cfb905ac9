/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  HTTPValidationError,
  MenuItem,
  SaveRequest,
  SectionDetails,
  SettingsSection,
} from '../models/index';
import {
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
    MenuItemFromJSON,
    MenuItemToJSON,
    SaveRequestFromJSON,
    SaveRequestToJSON,
    SectionDetailsFromJSON,
    SectionDetailsToJSON,
    SettingsSectionFromJSON,
    SettingsSectionToJSON,
} from '../models/index';

export interface SettingsGetSettingsDetailsRouteRequest {
    identifier?: string;
}

export interface SettingsSaveRequest {
    saveRequest: SaveRequest;
}

export interface SettingsUpdateSelectSettingRequest {
    id: string;
    value: string;
}

export interface SettingsUpdateToggleSettingRequest {
    id: string;
    enabled: boolean;
}

/**
 * 
 */
export class SettingsApi extends runtime.BaseAPI {

    /**
     * Get Settings Details Route
     */
    async settingsGetSettingsDetailsRouteRaw(requestParameters: SettingsGetSettingsDetailsRouteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<SectionDetails>> {
        const queryParameters: any = {};

        if (requestParameters['identifier'] != null) {
            queryParameters['identifier'] = requestParameters['identifier'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/settings/details`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => SectionDetailsFromJSON(jsonValue));
    }

    /**
     * Get Settings Details Route
     */
    async settingsGetSettingsDetailsRoute(requestParameters: SettingsGetSettingsDetailsRouteRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<SectionDetails> {
        const response = await this.settingsGetSettingsDetailsRouteRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Get Settings Menu Route
     */
    async settingsGetSettingsMenuRouteRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<MenuItem>>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/settings/menu`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(MenuItemFromJSON));
    }

    /**
     * Get Settings Menu Route
     */
    async settingsGetSettingsMenuRoute(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<MenuItem>> {
        const response = await this.settingsGetSettingsMenuRouteRaw(initOverrides);
        return await response.value();
    }

    /**
     * List Settings
     */
    async settingsListSettingsRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<SettingsSection>>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/settings/list`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(SettingsSectionFromJSON));
    }

    /**
     * List Settings
     */
    async settingsListSettings(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<SettingsSection>> {
        const response = await this.settingsListSettingsRaw(initOverrides);
        return await response.value();
    }

    /**
     * Save
     */
    async settingsSaveRaw(requestParameters: SettingsSaveRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<number>> {
        if (requestParameters['saveRequest'] == null) {
            throw new runtime.RequiredError(
                'saveRequest',
                'Required parameter "saveRequest" was null or undefined when calling settingsSave().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/settings/save`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: SaveRequestToJSON(requestParameters['saveRequest']),
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<number>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Save
     */
    async settingsSave(requestParameters: SettingsSaveRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<number> {
        const response = await this.settingsSaveRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Update Select Setting
     */
    async settingsUpdateSelectSettingRaw(requestParameters: SettingsUpdateSelectSettingRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling settingsUpdateSelectSetting().'
            );
        }

        if (requestParameters['value'] == null) {
            throw new runtime.RequiredError(
                'value',
                'Required parameter "value" was null or undefined when calling settingsUpdateSelectSetting().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['id'] != null) {
            queryParameters['id'] = requestParameters['id'];
        }

        if (requestParameters['value'] != null) {
            queryParameters['value'] = requestParameters['value'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/settings/updateSelect`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Update Select Setting
     */
    async settingsUpdateSelectSetting(requestParameters: SettingsUpdateSelectSettingRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.settingsUpdateSelectSettingRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Update Toggle Setting
     */
    async settingsUpdateToggleSettingRaw(requestParameters: SettingsUpdateToggleSettingRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['id'] == null) {
            throw new runtime.RequiredError(
                'id',
                'Required parameter "id" was null or undefined when calling settingsUpdateToggleSetting().'
            );
        }

        if (requestParameters['enabled'] == null) {
            throw new runtime.RequiredError(
                'enabled',
                'Required parameter "enabled" was null or undefined when calling settingsUpdateToggleSetting().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['id'] != null) {
            queryParameters['id'] = requestParameters['id'];
        }

        if (requestParameters['enabled'] != null) {
            queryParameters['enabled'] = requestParameters['enabled'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/settings/updateToggle`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Update Toggle Setting
     */
    async settingsUpdateToggleSetting(requestParameters: SettingsUpdateToggleSettingRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.settingsUpdateToggleSettingRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
