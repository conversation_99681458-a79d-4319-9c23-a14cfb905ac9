import { useCallback, useState } from "react";

import { VirtualizedCombobox } from "~/@ui/VirtualizedCombobox";
import { ApiRoutersCrmClientResponse } from "~/api/openapi/generated";
import LabeledValue from "~/types/LabeledValue";

type CrmClientsDropdownType = {
  placeholder?: string;
  leftIcon?: React.ReactNode;
  onChange: (value: LabeledValue | undefined) => void;
  selectedObject?: LabeledValue;
  searchOnLabel?: boolean;
  modal?: boolean;
  disabled?: boolean;
  itemSize?: number;
  maxHeightPx?: number;
};

const CrmClientsDropdown = (props: CrmClientsDropdownType) => {
  const {
    placeholder,
    leftIcon,
    onChange,
    selectedObject,
    searchOnLabel,
    modal,
    disabled,
    itemSize = 40,
    maxHeightPx = 200,
  } = props;

  const [clients, setClients] = useState([]);
  const [lastSearchTerm, setLastSearchTerm] = useState("");
  const [cursor, setCursor] = useState("");
  const [isFirstApiCall, setIsFirstApiCall] = useState(true); // true if this is the first time the API is called

  const loadClientsForCombobox = useCallback(
    async (searchTerm: string) => {
      let updatedSearchTerm = lastSearchTerm;
      let updatedCursor = cursor;
      let hasSearchTermChanged = false;

      if (lastSearchTerm !== searchTerm) {
        hasSearchTermChanged = true;
        updatedSearchTerm = searchTerm;
        updatedCursor = "";
      }

      setLastSearchTerm(updatedSearchTerm);
      setCursor(updatedCursor);

      if (!hasSearchTermChanged && !updatedCursor && !isFirstApiCall) {
        return;
      }

      setIsFirstApiCall(false);

      const { clients: options, nextPageToken } = await fetch(
        `/api/clients/get?searchTerm=${updatedSearchTerm}&cursor=${updatedCursor}&pageSize=20`
      ).then((res) => res.json());

      setCursor(nextPageToken);
      setClients(hasSearchTermChanged ? options : [...clients, ...options]);
    },
    [cursor, clients, lastSearchTerm, isFirstApiCall]
  );

  const options = clients.map((client: ApiRoutersCrmClientResponse) => ({
    value: client.uuid,
    label: client.name,
  }));

  return (
    <VirtualizedCombobox
      options={options}
      placeholder={placeholder}
      leftIcon={leftIcon}
      onChange={onChange}
      disabled={disabled}
      selectedObject={selectedObject}
      searchOnLabel={searchOnLabel}
      loadOptions={loadClientsForCombobox}
      modal={modal}
      itemSize={itemSize}
      maxHeightPx={maxHeightPx}
    />
  );
};

export default CrmClientsDropdown;
