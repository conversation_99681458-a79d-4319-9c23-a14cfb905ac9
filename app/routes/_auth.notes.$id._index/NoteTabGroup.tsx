import { MoreH<PERSON>z, Refresh, Warning } from "@mui/icons-material";
import { SerializeFrom } from "@remix-run/node";
import { ReactElement, type ReactNode } from "react";
import { Button } from "~/@shadcn/ui/button";
import { ToggleGroup, ToggleGroupItem } from "~/@shadcn/ui/toggle-group";
import { cn } from "~/@shadcn/utils";
import { Spinner } from "~/@ui/assets/Spinner";
import { FollowUpStatus, NoteResponse } from "~/api/openapi/generated";
import type { RevalidationState } from "@remix-run/router";

// Helpers
export const hasSummary = (note: SerializeFrom<NoteResponse>) =>
  (note.summaryByTopics?.sections?.length ?? 0) > 0;
export const hasTranscript = (note: SerializeFrom<NoteResponse>) =>
  (note.transcript.utterances?.length ?? 0) > 0;

// Fragments
type TabContentsProps = {
  activeTab: string;
  children: ReactNode;
  value: string;
};
const TabContents = ({ activeTab, children, value }: TabContentsProps) => (
  <div className={cn("flex flex-col gap-2", value !== activeTab && "hidden")}>
    {children}
  </div>
);

// Exports
export interface FollowUpTab {
  title: string;
  status: FollowUpStatus;
  uuid: string;
  tab: ReactElement;
}

export const NoteTabGroup = ({
  note,
  detailsTab,
  summaryTab,
  transcriptTab,
  prepTab,
  followUpTabs = [],
  currentTab = "details",
  setCurrentTab,
  revalidator,
}: {
  note: SerializeFrom<NoteResponse>;
  detailsTab: ReactNode;
  summaryTab: ReactNode;
  transcriptTab: ReactNode;
  prepTab: ReactNode;
  followUpTabs: FollowUpTab[];
  currentTab: string;
  setCurrentTab: (tab: string) => void;
  revalidator: {
    revalidate: () => void;
    state: RevalidationState;
  };
}) => {
  return (
    <>
      <div className="flex">
        <ToggleGroup
          className="flex w-full flex-wrap justify-start"
          value={currentTab}
          type="single"
          onValueChange={(value) => {
            if (value !== "") setCurrentTab(value);
          }}
          data-onboarding="notes-navigation"
        >
          <ToggleGroupItem value="details" aria-label="Show details">
            Details
          </ToggleGroupItem>
          {summaryTab && hasSummary(note) && (
            <ToggleGroupItem value="summary" aria-label="Show summary">
              Summary
            </ToggleGroupItem>
          )}
          {transcriptTab && hasTranscript(note) && (
            <ToggleGroupItem value="transcript" aria-label="Show transcript">
              Transcript
            </ToggleGroupItem>
          )}
          {prepTab && (
            <ToggleGroupItem value="prep" aria-label="Show meeting prep">
              Meeting prep
            </ToggleGroupItem>
          )}
          {followUpTabs.map(({ title, status, uuid }, index) => (
            <ToggleGroupItem key={index} value={uuid}>
              {status === FollowUpStatus.Processing && <MoreHoriz />}
              {(status === FollowUpStatus.Failed ||
                status === FollowUpStatus.Unknown) && <Warning color="error" />}
              {title}
            </ToggleGroupItem>
          ))}
        </ToggleGroup>
        {followUpTabs.find(
          ({ status }) => status === FollowUpStatus.Processing
        ) && (
          <Button
            variant="ghost"
            size="icon"
            onClick={revalidator.revalidate}
            disabled={revalidator.state === "loading"}
          >
            {revalidator.state === "loading" ? (
              <Spinner data-testid="spinner" />
            ) : (
              <Refresh />
            )}
          </Button>
        )}
      </div>
      <TabContents activeTab={currentTab} value="details">
        {detailsTab}
      </TabContents>
      {summaryTab && hasSummary(note) && (
        <TabContents activeTab={currentTab} value="summary">
          {summaryTab}
        </TabContents>
      )}
      {transcriptTab && hasTranscript(note) && (
        <TabContents activeTab={currentTab} value="transcript">
          {transcriptTab}
        </TabContents>
      )}
      {prepTab && (
        <TabContents activeTab={currentTab} value="prep">
          {prepTab}
        </TabContents>
      )}
      {followUpTabs.map(({ uuid, tab }, index) => (
        <TabContents key={index} activeTab={currentTab} value={uuid}>
          {tab}
        </TabContents>
      ))}
    </>
  );
};
