import { render, screen } from "@testing-library/react";
import { DetailsTab } from "./DetailsTab";
import { NoteType, ProcessingStatus } from "~/api/openapi/generated";
import { createMemoryRouter, RouterProvider } from "react-router-dom";

describe("DetailsTab", () => {
  const note = {
    uuid: "1",
    meetingName: "Test Meeting",
    status: ProcessingStatus.Processed,
    noteType: NoteType.MeetingRecording,
    created: "2023-04-01T10:00:00.000Z",
    modified: "2023-04-01T10:00:00.000Z",
    meetingDurationSeconds: 10,
    meetingTypeUuid: null,
    meetingCategory: "client",
    actionItems: [
      { uuid: "1", content: "Action Item 1", status: "complete" },
      { uuid: "2", content: "Action Item 2", status: "incomplete" },
      { uuid: "3", content: "Action Item 3", status: "incomplete" },
    ],
    advisorNotes: ["Advisor Note 1", "Advisor Note 2"],
    keyTakeaways: ["Key Takeaway 1", "Key Takeaway 2"],
    client: undefined,
    transcript: { utterances: [] },
    summaryByTopics: { sections: [] },
    isDeleted: false,
    botId: undefined,
    features: [],
  };

  it("renders action items correctly", () => {
    const router = createMemoryRouter(
      [{ path: "/", element: <DetailsTab note={note} /> }],
      { initialEntries: ["/"] }
    );
    render(<RouterProvider router={router} />);
    expect(screen.getByText("Action items")).toBeInTheDocument();
    expect(screen.getByText("Action Item 1")).toBeInTheDocument();
    expect(screen.getByText("Action Item 2")).toBeInTheDocument();
  });

  it("renders checked action items correctly", () => {
    const noteWithCheckedItem = {
      ...note,
      actionItems: [
        { uuid: "1", content: "Action Item 1", status: "complete" },
      ],
    };
    const router = createMemoryRouter(
      [{ path: "/", element: <DetailsTab note={noteWithCheckedItem} /> }],
      { initialEntries: ["/"] }
    );
    render(<RouterProvider router={router} />);
    expect(screen.getByRole("checkbox", { checked: true })).toBeInTheDocument();
  });

  it("renders unchecked action items correctly", () => {
    const noteWithCheckedItem = {
      ...note,
      actionItems: [
        { uuid: "1", content: "Action Item 1", status: "incomplete" },
      ],
    };
    const router = createMemoryRouter(
      [{ path: "/", element: <DetailsTab note={noteWithCheckedItem} /> }],
      { initialEntries: ["/"] }
    );
    render(<RouterProvider router={router} />);
    expect(
      screen.getByRole("checkbox", { checked: false })
    ).toBeInTheDocument();
  });

  it("renders 'No action items' message when no action items are provided", () => {
    const noteDataWithoutActionItems = {
      ...note,
      actionItems: [],
    };
    render(<DetailsTab note={noteDataWithoutActionItems} />);
    expect(screen.getByText("No action items.")).toBeInTheDocument();
  });

  it("renders key takeaways correctly", () => {
    const router = createMemoryRouter(
      [{ path: "/", element: <DetailsTab note={note} /> }],
      { initialEntries: ["/"] }
    );
    render(<RouterProvider router={router} />);
    expect(screen.getByText("Key takeaways")).toBeInTheDocument();
    expect(screen.getByText("Key Takeaway 1")).toBeInTheDocument();
    expect(screen.getByText("Key Takeaway 2")).toBeInTheDocument();
  });

  it("renders 'No takeaways' message when no key takeaways are provided", () => {
    const noteWithoutKeyTakeaways = {
      ...note,
      keyTakeaways: [],
    };
    const router = createMemoryRouter(
      [{ path: "/", element: <DetailsTab note={noteWithoutKeyTakeaways} /> }],
      { initialEntries: ["/"] }
    );
    render(<RouterProvider router={router} />);
    expect(screen.getByText("Key takeaways")).toBeInTheDocument();
    expect(screen.getByText("No takeaways.")).toBeInTheDocument();
  });

  it("renders advisor notes correctly", () => {
    const router = createMemoryRouter(
      [{ path: "/", element: <DetailsTab note={note} /> }],
      { initialEntries: ["/"] }
    );
    render(<RouterProvider router={router} />);
    expect(screen.getByText("Advisor notes")).toBeInTheDocument();
    expect(screen.getByText("Advisor Note 1")).toBeInTheDocument();
    expect(screen.getByText("Advisor Note 2")).toBeInTheDocument();
  });

  it("renders 'No notes' message when no advisor notes are provided", () => {
    const noteWithoutAdvisorNotes = {
      ...note,
      advisorNotes: [],
    };
    const router = createMemoryRouter(
      [{ path: "/", element: <DetailsTab note={noteWithoutAdvisorNotes} /> }],
      { initialEntries: ["/"] }
    );
    render(<RouterProvider router={router} />);
    expect(screen.getByText("Advisor notes")).toBeInTheDocument();
    expect(screen.getByText("No notes.")).toBeInTheDocument();
  });
});
