import { ClientInteraction, NoteResponse } from "~/api/openapi/generated";
import { EditableNoteState } from "./editableNoteReducer";
import { AttendeeOption, AttendeeOptions } from "~/api/attendees/types";

type LoaderData = {
  note: NoteResponse;
  interaction: ClientInteraction | null; // Interaction data (replace with actual type if known)
  clients: { uuid: string; name: string }[];
  initialAttendees: AttendeeOptions;
  attendeeOptions: AttendeeOptions;
  userOptions: AttendeeOptions;
  userAsAttendee: AttendeeOption | null;
  clientSelectionEnabled: boolean;
  reducerInitialState: EditableNoteState;
  parsedFollowUps: any[]; // Replace with actual type if known
};

const fakeLoaderData: LoaderData = {
  note: {
    uuid: "intro_mock_uuid",
    created: new Date("2025-04-11T16:40:34.251Z"),
    modified: new Date("2025-05-02T13:25:58.722Z"),
    noteType: "voice_memo",
    status: "processed",
    meetingName: "Zeplyn Onboarding Meeting",
    meetingDurationSeconds: 961,
    meetingTypeUuid: null,
    meetingCategory: "client",
    attendees: [
      {
        uuid: "2b1ff4cc-fa60-440d-80f6-56ad8789ddcf",
        name: "<PERSON> Chen",
        type: "unknown",
        speakerTime: "0:04:07",
        speakerPercentage: 25.2,
        speakerAlias: "speaker2",
      },
      {
        uuid: "943286ae-dda6-453a-b2f4-ae6f8b541f7f",
        name: "Lisa Chen",
        type: "unknown",
        speakerTime: "0:04:15",
        speakerPercentage: 26.3,
        speakerAlias: "speaker1",
      },
      {
        uuid: "fe3d8bb5-84a0-4258-a6b4-f4047f67b512",
        name: "Michael Rodriguez",
        type: "unknown",
        speakerTime: "0:07:59",
        speakerPercentage: 48.5,
        speakerAlias: "speaker0",
      },
    ],
    tags: [
      "Retirement planning",
      "401k",
      "IRA rollover",
      "Asset allocation",
      "Dividend stocks",
      "Tax planning",
      "College savings",
      "Emergency fund",
    ],
    actionItems: [
      {
        uuid: "4e62ec7b-b54e-4fce-b773-3f75323b2af1",
        content:
          "Schedule follow-up meeting with Chen family to review revised financial plan",
        status: "incomplete",
      },
      {
        uuid: "11138c7c-9499-48d8-ac65-0da8037e986f",
        content: "Process IRA rollover paperwork for James Chen's old 401(k)",
        status: "incomplete",
      },
    ],
    advisorNotes: [
      "James and Lisa Chen are concerned about optimizing their retirement contributions after Lisa's recent promotion.",
      "Family is considering purchasing a vacation property within the next 3-5 years which impacts current investment strategy.",
    ],
    keyTakeaways: [
      "Michael Rodriguez met with clients James and Lisa Chen to discuss retirement planning strategies in light of Lisa's recent promotion to Senior Marketing Director.",
      "Lisa's annual salary has increased from $85,000 to $120,000, and they need to adjust retirement contributions to optimize tax advantages.",
    ],
    transcript: {
      utterances: [
        {
          speaker: "Michael Rodriguez",
          start: "0:00:03.220000",
          end: "0:01:15.640000",
          text: "Hello James and Lisa, thank you for coming in today. I understand we're meeting to discuss your retirement planning strategy, especially in light of Lisa's recent promotion to Senior Marketing Director. Congratulations on that achievement, by the way! I'd like to start by reviewing your current financial situation and then move on to discussing some adjustments we might want to make to your retirement planning. From the information you provided before our meeting, I see that your combined annual income is now about $235,000, with James at $115,000 and Lisa at $120,000 after her promotion. You have about $435,000 in retirement accounts across your 401(k)s and IRAs, an emergency fund of $30,000, and college savings for your two children. Does that summary sound accurate, or is there anything major I've missed?",
        },
        {
          speaker: "Lisa Chen",
          start: "0:01:16.200000",
          end: "0:01:55.560000",
          text: "Thanks, Michael. Yes, that's correct. My promotion came with a substantial salary increase from $85,000 to $120,000, so we're trying to figure out the best way to adjust our retirement contributions and overall financial plan. We're particularly interested in maximizing the tax advantages of my new income level. Also, we have been thinking about buying a vacation property in Colorado in the next few years, so we'd like to incorporate that goal into our planning as well. Oh, and James has an old 401(k) from his previous job that we've been meaning to roll over but haven't gotten around to it yet.",
        },
        {
          speaker: "James Chen",
          start: "0:01:56.500000",
          end: "0:02:22.360000",
          text: "Yeah, that 401(k) has about $78,000 in it, but the investment options aren't great and the fees are higher than I'd like. We've been with my current employer for almost four years now, but never dealt with rolling over that old account. Also, with Lisa's promotion, we're wondering if we should be contributing differently to our retirement accounts. Currently, we're both contributing about 15% of our salaries.",
        },
        {
          speaker: "Michael Rodriguez",
          start: "0:02:23.420000",
          end: "0:03:30.240000",
          text: "Excellent! I'll have my assistant schedule both the follow-up meeting in three weeks and a separate insurance review session. Tomorrow morning works perfectly for dropping off the 401(k) statement. I'll prepare a comprehensive financial plan update based on everything we've discussed today, including the retirement contribution adjustments, college savings increases, vacation property saving strategy, and tax planning recommendations. Thank you both for coming in today, and congratulations again on your promotion, Lisa. I look forward to seeing you again in a few weeks.",
        },
      ],
    },
    summaryByTopics: {
      sections: [
        {
          topic: "RETIREMENT PLANNING OVERVIEW",
          bullets: [
            "Lisa's promotion to Senior Marketing Director has increased their household income to $235,000 annually, creating opportunities to optimize retirement savings.",
            "Current retirement accounts total $435,000 with contributions at 15% of gross income, recommended to increase to 18-20%.",
          ],
        },
        {
          topic: "COLLEGE SAVINGS & EDUCATION PLANNING",
          bullets: [
            "The Chens have two children ages 8 and 10 with 529 plan balances of $45,000 and $55,000 respectively.",
            "Current monthly contributions of $500 per child should be increased to $650 per child to meet projected education costs.",
          ],
        },
      ],
    },
    isDeleted: false,
    features: [],
    authorizedUserUuids: ["37a32302-827f-41ce-984a-67b511bb0cbd"],
  },
  interaction: null,
  userAsAttendee: null,
  clients: [],
  initialAttendees: [
    {
      name: "James Chen",
      uuid: "2b1ff4cc-fa60-440d-80f6-56ad8789ddcf",
      type: "unknown",
    },
    {
      name: "Lisa Chen",
      uuid: "943286ae-dda6-453a-b2f4-ae6f8b541f7f",
      type: "unknown",
    },
    {
      name: "Michael Rodriguez",
      uuid: "fe3d8bb5-84a0-4258-a6b4-f4047f67b512",
      type: "unknown",
    },
  ],
  attendeeOptions: [
    {
      uuid: "b980c4a6-68fa-4df1-9fc4-e1eb387a0584",
      name: "Debojyoti Ghosh",
      type: "user",
    },
    {
      name: "James Chen",
      uuid: "2b1ff4cc-fa60-440d-80f6-56ad8789ddcf",
      type: "unknown",
    },
    {
      name: "Lisa Chen",
      uuid: "943286ae-dda6-453a-b2f4-ae6f8b541f7f",
      type: "unknown",
    },
    {
      name: "Michael Rodriguez",
      uuid: "fe3d8bb5-84a0-4258-a6b4-f4047f67b512",
      type: "unknown",
    },
    {
      uuid: "79f0c849-baa5-4cea-a175-f6d2ee257c20",
      name: "Qwerty",
      type: "user",
    },
  ],
  userOptions: [
    {
      uuid: "b980c4a6-68fa-4df1-9fc4-e1eb387a0584",
      name: "Debojyoti Ghosh",
      type: "user",
    },
    {
      uuid: "79f0c849-baa5-4cea-a175-f6d2ee257c20",
      name: "Qwerty",
      type: "user",
    },
  ],
  clientSelectionEnabled: false,
  reducerInitialState: {
    touched: false,
    meetingName: "Sample Meeting with James and Lisa Chen",
    client: null,
    actionItems: [
      {
        uuid: "4e62ec7b-b54e-4fce-b773-3f75323b2af1",
        value:
          "Schedule follow-up meeting with Chen family to review revised financial plan",
        checked: false,
        action: "keep",
        autoFocus: false,
      },
      {
        uuid: "11138c7c-9499-48d8-ac65-0da8037e986f",
        value: "Process IRA rollover paperwork for James Chen's old 401(k)",
        checked: false,
        action: "keep",
        autoFocus: false,
      },
    ],
    advisorNotes: [],
    keyTakeaways: [],
    summaryByTopics: [
      {
        autoFocus: false,
        uuid: "96975e60-90c3-4a27-87f1-6f3792263c8a",
        value: "RETIREMENT PLANNING OVERVIEW",
        nodes: [
          {
            autoFocus: false,
            uuid: "f4c34061-1f9a-4f8a-b492-a90ede838c9c",
            value:
              "Lisa's promotion to Senior Marketing Director has increased their household income to $235,000 annually, creating opportunities to optimize retirement savings.",
          },
          {
            autoFocus: false,
            uuid: "ab306c81-622d-43ac-a3a7-b540fb3a7d40",
            value:
              "Current retirement accounts total $435,000 with contributions at 15% of gross income, recommended to increase to 18-20%.",
          },
          {
            autoFocus: false,
            uuid: "99338fa5-2787-48e9-bfa1-d2600f5e112c",
            value:
              "James has an old 401k worth $78,000 from his previous employer that should be rolled over to an IRA for better investment options and lower fees.",
          },
          {
            autoFocus: false,
            uuid: "6b65ba4a-33fe-49fd-9b3f-ce7daa5cebe8",
            value:
              "Based on current savings rate and projected returns, they are on track to reach approximately $2.8 million by retirement age 65, but could optimize further.",
          },
          {
            autoFocus: false,
            uuid: "0580b17b-9f83-41b1-9457-d0ec1307fccf",
            value:
              "Recommended adjusting 401k allocations to include more international exposure and slightly more conservative asset mix (65/35 instead of current 70/30).",
          },
        ],
      },
      {
        autoFocus: false,
        uuid: "a0a451ca-1932-47dd-b549-e4aba55d397b",
        value: "COLLEGE SAVINGS & EDUCATION PLANNING",
        nodes: [
          {
            autoFocus: false,
            uuid: "01e8be0f-916c-4b41-8f04-3bf35523fda1",
            value:
              "The Chens have two children ages 8 and 10 with 529 plan balances of $45,000 and $55,000 respectively.",
          },
          {
            autoFocus: false,
            uuid: "09d39430-6136-4a02-9b40-d4d8c6d85fd6",
            value:
              "Current monthly contributions of $500 per child should be increased to $650 per child to meet projected education costs.",
          },
          {
            autoFocus: false,
            uuid: "3b469a94-e3cd-4ba2-a0e0-00e5956141c9",
            value:
              "Based on 5% annual college cost inflation, estimated costs for 4-year public university will be $160,000 and $180,000 for their children.",
          },
          {
            autoFocus: false,
            uuid: "8ef1d115-5636-4bd4-b718-4312c3544e5f",
            value:
              "Suggested exploring merit scholarship opportunities and grants to supplement savings.",
          },
          {
            autoFocus: false,
            uuid: "bfaaeeab-4e71-44ce-bf21-c2c20e263c52",
            value:
              "Discussed tax benefits of 529 plans and potential alternative uses if children receive scholarships.",
          },
        ],
      },
      {
        autoFocus: false,
        uuid: "9f8b3b8c-1698-42d2-b057-5f9733a44824",
        value: "VACATION HOME PURCHASE PLANNING",
        nodes: [
          {
            autoFocus: false,
            uuid: "a94c6e17-e4f1-49e9-8b42-337eff8fb0f2",
            value:
              "The Chen family expressed interest in purchasing a vacation property in Colorado within 3-5 years.",
          },
          {
            autoFocus: false,
            uuid: "a50abf31-71f7-440b-a6a8-0bcebc514738",
            value:
              "Estimated down payment needed ranges from $75,000 to $100,000 based on properties in their desired location.",
          },
          {
            autoFocus: false,
            uuid: "b526ea89-b02a-4fa2-b15d-9f1c9555112c",
            value:
              "Recommended establishing a dedicated savings account with monthly contributions of $1,500-$2,000 to reach this goal.",
          },
          {
            autoFocus: false,
            uuid: "7fe63ce0-9bb0-4a1c-b73d-2698c20adb29",
            value:
              "Discussed potential tax implications and benefits of second home ownership, including mortgage interest deductions.",
          },
          {
            autoFocus: false,
            uuid: "bf3b1b04-df82-444b-84e9-2251a225930d",
            value:
              "Analyzed impact of vacation home purchase on overall financial plan, including retirement savings and college funding goals.",
          },
        ],
      },
      {
        autoFocus: false,
        uuid: "577586e4-7e4c-47c9-8e2a-1bc73ff3cdf4",
        value: "TAX PLANNING STRATEGIES",
        nodes: [
          {
            autoFocus: false,
            uuid: "459a6442-627d-409f-a1b2-3aabea86f94c",
            value:
              "With increased household income, more aggressive tax planning strategies are needed to minimize tax burden.",
          },
          {
            autoFocus: false,
            uuid: "05b24b70-4dd9-4a68-93e6-465e0fc5d3ea",
            value:
              "Recommended maximizing pre-tax retirement contributions to reduce taxable income.",
          },
          {
            autoFocus: false,
            uuid: "61ac57aa-6269-4451-a400-60c07bc36c7f",
            value:
              "Discussed tax-loss harvesting opportunities in their taxable investment accounts.",
          },
          {
            autoFocus: false,
            uuid: "092f640b-3102-4cc6-8d7d-8810a510a161",
            value:
              "Explored potential benefits of establishing a donor-advised fund for their charitable giving plans.",
          },
          {
            autoFocus: false,
            uuid: "5ad12216-4ef4-43af-9809-db28d4bd356c",
            value:
              "Analyzed Roth conversion strategies for a portion of their traditional IRA assets to diversify tax exposure in retirement.",
          },
        ],
      },
    ],
    attendees: [
      {
        name: "James Chen",
        uuid: "2b1ff4cc-fa60-440d-80f6-56ad8789ddcf",
        type: "unknown",
      },
      {
        name: "Lisa Chen",
        uuid: "943286ae-dda6-453a-b2f4-ae6f8b541f7f",
        type: "unknown",
      },
      {
        name: "Michael Rodriguez",
        uuid: "fe3d8bb5-84a0-4258-a6b4-f4047f67b512",
        type: "unknown",
      },
    ],
  },
  parsedFollowUps: [
    {
      followUp: {
        uuid: "2bc3a582-3349-431c-88b4-e6f118793aca",
        title: "Life Events",
        kind: "bento_life_events",
        schema: {
          $id: "https://zeplyn.ai/structured_review_data.schema.json",
          type: "object",
          $defs: {
            review_entry: {
              type: "object",
              required: ["id", "kind", "topic"],
              properties: {
                id: {
                  type: "string",
                },
                kind: {
                  enum: ["header", "toggle", "select", "multiselect"],
                  type: "string",
                },
                topic: {
                  type: "string",
                },
                options: {
                  type: "array",
                  items: {
                    type: "string",
                  },
                },
                evidence: {
                  type: "array",
                  items: {
                    $ref: "#/$defs/evidence_entry",
                  },
                },
                selected: {
                  type: "string",
                },
                discussed: {
                  type: "boolean",
                },
                subentries: {
                  type: "array",
                  items: {
                    $ref: "#/$defs/review_entry",
                  },
                },
                explanation: {
                  type: "string",
                },
                multi_selected: {
                  type: "array",
                  items: {
                    type: "string",
                  },
                },
              },
              additionalProperties: false,
            },
            evidence_entry: {
              type: "object",
              required: ["quote"],
              properties: {
                quote: {
                  type: "string",
                },
                timestamp: {
                  type: "string",
                },
              },
              additionalProperties: false,
            },
          },
          title: "Structured meeting review data",
          $schema: "https://json-schema.org/draft/2020-12/schema",
          required: ["review_entries"],
          properties: {
            review_entries: {
              type: "array",
              items: {
                $ref: "#/$defs/review_entry",
              },
              uniqueItems: true,
            },
          },
          description:
            "Describes the format of structured meeting follow-up data that is generated from a transcript of a meeting.",
          additionalProperties: false,
        },
        data: {
          review_entries: [],
        },
        status: "completed",
      },
      parsedData: {
        review_entries: [
          {
            id: "birth_or_adoption",
            kind: "toggle",
            topic: "Birth or Adoption",
            discussed: false,
            explanation:
              "The topic of birth or adoption was not discussed in the meeting transcript.",
            evidence: [],
          },
          {
            id: "saving_and_investing",
            kind: "toggle",
            topic: "Saving and Investing",
            discussed: true,
            explanation:
              "The meeting focused heavily on saving and investing strategies, including maximizing 401(k) contributions, rolling over an old 401(k) to an IRA, adjusting asset allocation, and increasing international exposure in their portfolio.",
            evidence: [
              {
                quote:
                  "I'd recommend that you both try to max out your 401(k) contributions this year, which is $22,500 each for 2023.",
                timestamp: "0:02:23.420000",
              },
              {
                quote:
                  "For the old 401(k), James, I absolutely recommend rolling that into an IRA. This will give you more investment options and likely lower fees.",
                timestamp: "0:02:23.420000",
              },
              {
                quote:
                  "I'd recommend having about 40% of your stock allocation in these types of investments.",
                timestamp: "0:06:58.420000",
              },
            ],
          },
          {
            id: "off_to_college",
            kind: "toggle",
            topic: "Off to College",
            discussed: true,
            explanation:
              "The meeting discussed college planning for their two children (ages 8 and 10), including current 529 plan balances and projections for college expenses.",
            evidence: [
              {
                quote:
                  "They're 8 and 10 years old now, so college isn't that far off.",
                timestamp: "0:03:31.200000",
              },
              {
                quote: "Our oldest will be starting college in about 8 years.",
                timestamp: "0:10:51.200000",
              },
            ],
          },
          {
            id: "age_of_majority",
            kind: "toggle",
            topic: "Age of Majority",
            discussed: false,
            explanation:
              "The topic of age of majority was not discussed in the meeting transcript.",
            evidence: [],
          },
        ],
      },
    },
  ],
};

export default fakeLoaderData;
