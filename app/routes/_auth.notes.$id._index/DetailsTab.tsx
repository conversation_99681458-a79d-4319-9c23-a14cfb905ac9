import { Typography } from "~/@ui/Typography";
import { TaskCard } from "~/@ui/tasks/TaskCard";
import { NoteResponse } from "~/api/openapi/generated";
import { SerializeFrom } from "@remix-run/node";

// Exports
export const DetailsTab = ({ note }: { note: SerializeFrom<NoteResponse> }) => (
  <>
    <Typography variant="h3" color="primary">
      Action items
    </Typography>
    {note.actionItems && note.actionItems.length > 0 ? (
      note.actionItems
        .sort((a, b) => a.content.localeCompare(b.content))
        .map((actionItem) => (
          <TaskCard
            key={actionItem.uuid}
            compact
            dueAt={null}
            to={`/tasks/${actionItem.uuid}`}
            uuid={actionItem.uuid}
            completed={actionItem.status === "complete"}
            title={actionItem.content}
          />
        ))
    ) : (
      <Typography color="secondary" variant="body2">
        No action items.
      </Typography>
    )}

    <Typography variant="h3" color="primary">
      Key takeaways
    </Typography>
    {note.keyTakeaways && note.keyTakeaways.length > 0 ? (
      <ul className="flex list-disc flex-col gap-2 pl-4 marker:text-warning">
        {note.keyTakeaways.map((keyTakeaway) => (
          <Typography asChild key={keyTakeaway} className="whitespace-pre-wrap">
            <li>{keyTakeaway}</li>
          </Typography>
        ))}
      </ul>
    ) : (
      <Typography color="secondary" variant="body2">
        No takeaways.
      </Typography>
    )}

    <Typography variant="h3" color="primary">
      Advisor notes
    </Typography>
    {note.advisorNotes && note.advisorNotes.length > 0 ? (
      <ul className="flex list-disc flex-col gap-2 pl-4 marker:text-warning">
        {note.advisorNotes.map((advisorNote) => (
          <Typography asChild key={advisorNote} className="whitespace-pre-wrap">
            <li>{advisorNote}</li>
          </Typography>
        ))}
      </ul>
    ) : (
      <Typography color="secondary" variant="body2">
        No notes.
      </Typography>
    )}
  </>
);
