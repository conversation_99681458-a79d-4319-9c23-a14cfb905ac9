import { render, screen } from "@testing-library/react";
import Route from "./route";
import { json } from "@remix-run/react";
import { createRemixStub } from "@remix-run/testing";
import { TooltipProvider } from "~/@shadcn/ui/tooltip";
import { mapNoteToReducerState } from "./editableNoteReducer";
import {
  MeetingCategory,
  NoteType,
  ProcessingStatus,
} from "~/api/openapi/generated";
import { ActionFunctionArgs } from "@remix-run/node";
import { LayoutV2 } from "~/@ui/layout/LayoutV2";
import { act } from "react-dom/test-utils";

describe("route", () => {
  it("should sort follow-up tabs correctly", async () => {
    const note = {
      attendees: [],
      authorizedUserUuids: [],
      created: new Date().toISOString(),
      followUps: [],
      isDeleted: false,
      tags: [],
      transcript: { utterances: [] },
      uuid: "note-uuid",
      meetingCategory: MeetingCategory.Client,
      meetingDurationSeconds: 3600,
      meetingName: "Test meeting",
      meetingTypeUuid: "uuid",
      modified: new Date().toISOString(),
      noteType: NoteType.MeetingRecording,
      status: ProcessingStatus.Processed,
    };
    const Empty = () => <div></div>;

    const WrappedRoute = () => (
      <TooltipProvider>
        <LayoutV2>
          <Route />
        </LayoutV2>
      </TooltipProvider>
    );
    const RemixStub = createRemixStub([
      {
        path: "/",
        Component: Empty,
      },
      {
        path: "/notes/note-uuid",
        Component: WrappedRoute,
        action({ request }: ActionFunctionArgs) {
          return null;
        },
        loader() {
          return json({
            note: note,
            clients: [],
            initialAttendees: [],
            attendeeOptions: [],
            reducerInitialState: mapNoteToReducerState(note, []),
            userOptions: [],
            userAsAttendee: null,
            isClientSelectionEnabled: false,
            parsedFollowUps: [
              {
                followUp: {
                  uuid: "2",
                  title: "B Follow-up",
                  schema: {
                    $id: "https://zeplyn.ai/structured_review_data.schema.json",
                  },
                },
                parsedData: { review_entries: [] },
              },
              {
                followUp: {
                  uuid: "3",
                  title: "A Follow-up",
                  schema: {
                    $id: "https://zeplyn.ai/structured_review_data.schema.json",
                  },
                },
                parsedData: {
                  review_entries: [{ id: "1", kind: "header", topic: "A two" }],
                },
              },
              {
                followUp: {
                  uuid: "1",
                  title: "A Follow-up",
                  schema: {
                    $id: "https://zeplyn.ai/structured_review_data.schema.json",
                  },
                },
                parsedData: {
                  review_entries: [{ id: "1", kind: "header", topic: "A one" }],
                },
              },
            ],
          });
        },
      },
    ]);

    render(
      <RemixStub initialEntries={["/notes/note-uuid"]} initialIndex={0} />
    );

    const followUpTabs = await screen.findAllByText(/Follow-up/);
    expect(followUpTabs[0]).toHaveTextContent("Preview Follow-up");
    expect(followUpTabs[1]).toHaveTextContent("A Follow-up");
    expect(followUpTabs[2]).toHaveTextContent("A Follow-up");
    expect(followUpTabs[3]).toHaveTextContent("B Follow-up");

    // Confirm the order of the tabs with the same name by checking which contents are displayed.
    act(() => {
      followUpTabs[1]?.click();
    });
    expect(
      screen.queryByText("A one")?.closest(".hidden")
    ).not.toBeInTheDocument();
    expect(screen.getByText("A two").closest(".hidden")).toBeInTheDocument();

    act(() => {
      followUpTabs[2]?.click();
    });
    expect(screen.getByText("A one").closest(".hidden")).toBeInTheDocument();
    expect(
      screen.queryByText("A two")?.closest(".hidden")
    ).not.toBeInTheDocument();
  });
});
