import { useState, useReducer, useEffect } from "react";
import { ClockIcon, PencilIcon, PencilOffIcon, TagIcon } from "lucide-react";

import { useFlag } from "~/context/flags";
import {
  json,
  redirect,
  LoaderFunctionArgs,
  MetaFunction,
  ActionFunction,
} from "@remix-run/node";
import {
  FastForwardOutlined,
  HelpOutline,
  OutboxOutlined,
  PeopleOutlined,
} from "@mui/icons-material";
import {
  unstable_usePrompt,
  useActionData,
  useLoaderData,
  useLocation,
  useRevalidator,
  useSubmit,
} from "@remix-run/react";
import { AttendeesSection } from "./AttendeesSection";
import { BackButton } from "~/@ui/buttons/BackButton";
import { Badge } from "~/@shadcn/ui/badge";
import { Button } from "~/@shadcn/ui/button";
import { StructuredDataReviewTab } from "./StructuredReviewDataTab";
import { ConfirmModal } from "~/@ui/ConfirmModal";
import { DeleteButton } from "~/@ui/buttons/DeleteButton";
import { DetailsEditableTab } from "~/routes/_auth.notes.$id._index/DetailsEditableTab";
import { EmailNoteButton } from "./EmailNoteButton";
import { FollowUpEmailButton } from "./FollowUpEmail";
import { FormAlertsStack, useFormErrors } from "~/@ui/FormAlertsStack";
import { FormField, FormLabel } from "~/@shadcn/ui/form";
import { HeaderV2, SidebarV2 } from "~/@ui/layout/LayoutV2";
import { NoteEditableTitle } from "~/@ui/notes/NoteEditableTitle";
import {
  FollowUpTab,
  NoteTabGroup,
} from "~/routes/_auth.notes.$id._index/NoteTabGroup";
import { Separator } from "~/@shadcn/ui/separator";
import { SummaryEditableTab } from "~/routes/_auth.notes.$id._index/SummaryEditableTab";
import { SyncToCrmButton } from "./SyncToCrmButton";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { TranscriptTab } from "~/routes/_auth.notes.$id._index/TranscriptTab";
import { Typography } from "~/@ui/Typography";
import { capitalize } from "~/utils/strings";
import {
  editableNoteReducer,
  mapNoteToReducerState,
  mapReducerStateToEditNoteArguments,
} from "~/routes/_auth.notes.$id._index/editableNoteReducer";
import { flattenZodErrors } from "~/utils/validation";
import { format } from "date-fns";
import { getClients } from "~/api/notes/getClients.server";
import { getNoteById } from "~/api/notes/getNoteById.server";
import { logError } from "~/utils/log.server";
import { secondsToDurationString } from "~/utils/time";
import { toast } from "react-toastify";
import { uploadNoteToCrm } from "~/api/notes/uploadToCrm.server";
import { z } from "zod";
import {
  INTERCOM_LAUNCHER_SELECTOR,
  useIntercom,
} from "~/third-party/Intercom/Intercom";
import { configurationParameters } from "~/api/openapi/configParams";
import {
  ApiRoutersCrmClientResponse,
  Configuration,
  FollowUpStatus,
  MeetingArtifactsApi,
  NoteApi,
  ProcessingStatus,
  SearchApi,
  SwapPair,
} from "~/api/openapi/generated";
import { AttendeesMultiSelect } from "~/@ui/attendees/AttendeeMultiSelect";
import { getAttendeeOptions } from "~/api/attendees/getAttendeeOptions.server";
import AskMeAnythingSection from "./AskMeAnythingSection";
import { getUserSessionOrRedirect } from "~/auth/authenticator.server";
import { ShareButton, ShareModal } from "./ShareModal";
import { resolvedAttendeeOptions } from "~/utils/attendeeOptions";
import { NumericalDataTab } from "./NumericalDataTab";
import {
  followUpDataForNote,
  NumericalData,
  numericalFollowUpDataSchemaID,
  structuredFollowUpDataSchemaID,
  StructuredReviewData,
} from "./followUpDataTypes";
import { Skeleton } from "~/@shadcn/ui/skeleton";
import MeetingPrepTab from "../_auth.notes.create.($id)/components/MeetingPrepTab";
import fakeLoaderData from "./fakeLoaderData";
import NotesOnboarding from "./components/NotesOnboarding";
import CrmClientsDropdown from "./components/CrmClientsDropdown";

const ERROR_MISSING_PARAMETER = 'Missing route parameter "id"';

// Types
const TabStruct = z.string();
type Tab = z.infer<typeof TabStruct>;

export const action: ActionFunction = async ({ params, request }) => {
  try {
    if (!params.id) throw Error(ERROR_MISSING_PARAMETER);
    const noteId = params.id;

    // Determine the content type of the request
    const contentType = request.headers.get("content-type");

    let data;
    if (contentType && contentType.includes("application/json")) {
      data = await request.json();
    } else if (contentType && contentType.includes("multipart/form-data")) {
      const formData = await request.formData();
      data = Object.fromEntries(formData.entries());
    } else {
      throw new Error("Unsupported content type");
    }

    const configuration = configurationParameters(request);
    const noteAPI = new NoteApi(new Configuration(await configuration));
    const searchAPI = new SearchApi(new Configuration(await configuration));
    const meetingArtifactsApi = new MeetingArtifactsApi(
      new Configuration(await configuration)
    );
    switch (data.actionType) {
      case "send-to-crm":
        const result = await uploadNoteToCrm({ noteId, request });
        if (result.success) {
          return json({ success: true });
        } else {
          return json({ success: false, error: result.error });
        }
      case "get-email-templates":
        const emailTemplates =
          await meetingArtifactsApi.meetingArtifactsMeetingSummaryEmailTemplates();
        return json({ success: true, templates: emailTemplates });

      case "send-follow-up-email":
        const emailContent = await noteAPI.noteEmailFollowupWithMailtoContent({
          noteId,
          template: data.templateId,
        });
        return json({
          success: true,
          mailtoLink: emailContent.mailtoLink,
        });
      case "search-note":
        try {
          const query = data.query;

          const { answer, searchQueryId } = await searchAPI.searchSearch({
            noteId,
            query,
          });

          return json({
            actionType: "search-note",
            success: true,
            answer: answer,
            searchQueryId: searchQueryId,
          });
        } catch (error) {
          logError("Error in action search-note", error);

          return json({
            actionType: "search-note",
            success: false,
            error: error,
          });
        }
      case "email-note":
        await noteAPI.noteEmailNote({ noteId });
        return json({ success: true });

      case "delete-note":
        await noteAPI.noteDeleteNote({ noteId });
        return redirect("/notes");

      case "send-attendee-remap":
        try {
          // Parse incoming data
          const parsedData = typeof data === "string" ? JSON.parse(data) : data;

          const { noteId, swaps } = parsedData;

          const parsedSwaps =
            typeof swaps === "string" ? JSON.parse(swaps) : swaps;

          // Validate that swaps is an array and noteId is a valid string
          if (!Array.isArray(parsedSwaps) || typeof noteId !== "string") {
            throw new Error(
              "Invalid data format: noteId or swaps missing or incorrect"
            );
          }

          // Call the remap function with the extracted and validated noteId and swaps
          noteAPI.noteSwapAttendeeAliases({
            swapAttendeesRequest: {
              noteId,
              swaps: parsedSwaps
                .map((swap: any) => {
                  if (
                    swap.current_alias === undefined ||
                    swap.new_alias === undefined
                  ) {
                    logError("Invalid swap data", swap);
                    return undefined;
                  }
                  return {
                    currentAlias: swap.current_alias,
                    newAlias: swap.new_alias,
                  };
                })
                .filter((f): f is SwapPair => !!f),
            },
          });
          return redirect("/notes");
        } catch (error) {
          logError("Error in send-attendee-remap", error);
          return json(
            { success: false, message: "Failed to remap attendees" },
            { status: 400 }
          );
        }
      case "update-meeting-follow-up":
        const parsedData = typeof data === "string" ? JSON.parse(data) : data;
        const { followUpId, followUpData } = parsedData;
        const parsedFollowUpData =
          typeof followUpData === "string"
            ? JSON.parse(followUpData)
            : followUpData;

        const configuration = new Configuration(
          await configurationParameters(request)
        );
        await new MeetingArtifactsApi(
          configuration
        ).meetingArtifactsUpdateFollowUp({
          followUpId,
          body: parsedFollowUpData,
        });
        return json({ success: true });

      case "share-note":
        try {
          const { authorizedUsers } = data;
          await noteAPI.noteUpdateAuthorizedUsers({
            noteId,
            requestBody: authorizedUsers,
          });
          return json({
            success: true,
            actionType: "share-note",
            nonce: Date.now(),
          });
        } catch (error) {
          logError("Error in action share-note", error);
          return json(
            {
              success: false,
              actionType: "share-note",
              nonce: Date.now(),
              errors: ["Failed to share note"],
            },
            { status: 500 }
          );
        }

      case "add-section-to-summary":
        try {
          const { searchQueryId } = data;
          const { sectionIndex } = await noteAPI.noteAddSectionToSummary({
            noteId: noteId,
            searchAddSectionRequest: {
              searchQueryId: searchQueryId,
            },
          });
          return json({
            success: true,
            sectionIndex,
            actionType: "add-section-to-summary",
            nonce: Date.now(),
          });
        } catch (error) {
          logError("Error in action add-section-to-summary", error);
          return json(
            {
              success: false,
              actionType: "add-section-to-summary",
              nonce: Date.now(),
              errors: ["Failed to add section to summary"],
            },
            { status: 500 }
          );
        }

      case "edit":
        const validatedData = JSON.parse(data.editRequest);
        await noteAPI.noteEditNote({
          noteId,
          editNoteRequest: validatedData,
        });
        return json({ success: true });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return json({ errors: flattenZodErrors(error) }, { status: 400 });
    }
    logError("app/routes/notes.$id.edit.tsx action", error);
    return json({ errors: ["Failed to save note"] }, { status: 500 });
  }
};

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  try {
    const noteId = params.id;

    if (!noteId) throw Error(ERROR_MISSING_PARAMETER);

    if (noteId === "intro_mock_uuid") {
      return fakeLoaderData;
    }

    const dataPromise = Promise.all([
      getUserSessionOrRedirect(request),
      getClients({ searchTerm: "", request }),
      getAttendeeOptions({ request }),
    ]);

    const note = await getNoteById({ noteId, request });
    const parsedFollowUps = followUpDataForNote(note);

    const configuration = new Configuration(
      await configurationParameters(request)
    );
    const meetingArtifactsApi = new MeetingArtifactsApi(configuration);
    const interactionPromise = note.interactionUuid
      ? meetingArtifactsApi
          .meetingArtifactsGetClientInteraction({
            interactionUuid: note.interactionUuid,
          })
          .catch(() => {
            return undefined;
          })
      : Promise.resolve(undefined);

    const [
      [{ userId }, { clients, clientSelectionEnabled }, attendeeOptions],
      interaction,
    ] = await Promise.all([dataPromise, interactionPromise]);
    const userOptions = attendeeOptions.filter((a) => a.type === "user");
    const userAsAttendee = attendeeOptions.find((item) => item.uuid === userId);
    const { initialAttendees, attendeeOptions: attendeeOptionsToUse } =
      resolvedAttendeeOptions(attendeeOptions, note.attendees, []);
    const reducerInitialState = mapNoteToReducerState(note, initialAttendees);

    return json({
      note,
      interaction,
      clients,
      initialAttendees,
      attendeeOptions: attendeeOptionsToUse,
      userOptions,
      userAsAttendee,
      clientSelectionEnabled,
      reducerInitialState,
      parsedFollowUps,
    });
  } catch (e) {
    logError("app/routes/notes.$id.edit.tsx loader error", e);
    return redirect("/notes");
  }
};

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `Note - ${data?.note.meetingName ?? "view details"}` },
    { name: "description", content: "Edit note" },
  ];
};

const ContactSupportTab = ({
  header,
  body,
}: {
  header: string;
  body: string;
}) => {
  const { isIntercomAvailable } = useIntercom();
  return (
    <div className="flex flex-col items-center justify-center gap-3 rounded-lg bg-yellow-50 p-6 text-gray-700 shadow-md">
      <Typography variant="h6" className="font-semibold">
        {header}
      </Typography>
      <Typography variant="body2" className="text-center">
        {body}
      </Typography>
      {isIntercomAvailable && (
        <div className="mt-4 flex items-center gap-2">
          <HelpOutline className="h-6 w-6 text-yellow-700" />
          <button
            id={INTERCOM_LAUNCHER_SELECTOR}
            className="text-sm font-medium text-yellow-700 underline hover:text-yellow-800"
          >
            Contact Support
          </button>
        </div>
      )}
    </div>
  );
};

const Route = () => {
  const {
    note: initialNote,
    interaction,
    clients,
    initialAttendees,
    attendeeOptions,
    reducerInitialState,
    userOptions,
    userAsAttendee,
    clientSelectionEnabled,
    parsedFollowUps,
  } = useLoaderData<typeof loader>();
  const enableManualSave = useFlag("EnableManualEditSaves") ?? false;
  const enableShareNote = useFlag("EnableShareNotes") ?? false;
  const enableEditAttendees = useFlag("EnableEditAttendees");
  const enableNoteSearch = useFlag("EnableSearchOnNote") ?? false;
  const enableLockdownMode = useFlag("EnableLockdownMode");
  const actionData = useActionData<typeof action>();
  const formErrors = useFormErrors(actionData);
  const revalidator = useRevalidator();
  const submit = useSubmit();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [editableNoteState, editableNoteDispatch] = useReducer(
    editableNoteReducer,
    reducerInitialState
  );
  const [note, setNote] = useState(initialNote);
  const [authorizedUsers, setAuthorizedUsers] = useState(
    (note.authorizedUserUuids ?? []).flatMap((uuid) => {
      const userOption = userOptions.find((user) => user.uuid === uuid);
      return userOption ? [userOption] : [];
    })
  );
  const [query, setQuery] = useState("");
  const [triggerSearch, setTriggerSearch] = useState(false);

  const startSearch = (searchString: string) => {
    setQuery(searchString);
    setTriggerSearch(true);
  };
  const [shareDialogErrorMessage, setShareDialogErrorMessage] = useState<
    string | null
  >(null);
  useEffect(() => {
    if (
      userAsAttendee &&
      !authorizedUsers.map((au) => au.uuid).includes(userAsAttendee.uuid)
    ) {
      setAuthorizedUsers((prev) => [userAsAttendee, ...prev]);
      setShareDialogErrorMessage("You can't un-share a note with yourself.");
    }
  }, [userAsAttendee, authorizedUsers, setShareDialogErrorMessage]);
  const [hasEdited, setHasEdited] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);

  const activeTabParam = searchParams.get("tab") as Tab | null;
  const topicParam = searchParams.get("topic");

  const activeTab = activeTabParam ? (activeTabParam as Tab) : "details";
  const summaryIndex = topicParam ? parseInt(topicParam) : null;
  const [currentTab, setCurrentTab] = useState<Tab>(activeTab);
  const [currentSummaryIndex, setCurrentSummaryIndex] = useState<number | null>(
    summaryIndex
  );

  const clientsWithCurrentClient = [
    ...clients,
    note.client
      ? { name: note.client.name, uuid: note.client.uuid }
      : undefined,
  ]
    .filter((c): c is ApiRoutersCrmClientResponse => !!c)
    .sort((a, b) => a.name?.localeCompare(b.name) ?? a.uuid > b.uuid);

  const getClientsForCombobox = () => {
    return clientsWithCurrentClient.map((client) => ({
      value: client.uuid,
      label: client.name,
    }));
  };

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const confirmDelete = () => {
    handleDelete();
    closeModal();
  };

  const handleDelete = () => {
    submit(
      { actionType: "delete-note" },
      {
        method: "post",
        encType: "application/json",
        replace: true,
      }
    );
  };

  const handleSave = () => {
    const request = mapReducerStateToEditNoteArguments(editableNoteState);
    submit(
      {
        editRequest: JSON.stringify(request),
        actionType: "edit",
      },
      {
        method: "post",
        encType: "application/json",
        replace: true,
      }
    );
  };

  const handleAddSectionToSummarySuccess = (sectionIndex: number | null) => {
    setCurrentTab("summary");
    if (sectionIndex !== null) setCurrentSummaryIndex(sectionIndex);
    editableNoteDispatch({
      type: "replaceState",
      newState: mapNoteToReducerState(initialNote, initialAttendees),
    });
  };

  const toggleEditMode = () => {
    if (isEditMode) {
      handleSave();
      setHasEdited(false);
      if (enableManualSave)
        toast.success("All changes saved.", {
          position: "top-right",
          className: "mt-16",
          autoClose: 1000,
          hideProgressBar: true,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "colored",
        });
    }
    setIsEditMode((prevMode) => !prevMode);
  };

  const enableEditMode = () => {
    setIsEditMode(true);
  };

  useEffect(() => {
    // Update the local `note` state with the latest `initialNote` from the server-side loader.
    // This ensures that the note variable used elsewhere reflects the most up-to-date version of the note.
    setNote(initialNote);

    // Dispatch the `replaceState` action to update the entire state in the reducer.
    // `replaceState` will take the new mapped state from the `initialNote` (which includes keyTakeaways, advisorNotes, etc.)
    // and completely overwrite the reducer's current state.
    // This ensures the local reducer's state stays consistent with the server-side data.
    editableNoteDispatch({
      type: "replaceState",
      newState: mapNoteToReducerState(initialNote, initialAttendees),
    });
    // This disable is necessary because we only want to re-run this effect when the note's status
    // or attendees change, not when `initialNote` changes.
    // eslint-disable-next-line
  }, [initialNote.status, initialNote.attendees]);

  useEffect(() => {
    if (
      note.status === ProcessingStatus.Finalized ||
      !isEditMode ||
      enableManualSave
    )
      return;
    const saveTimeoutId = setTimeout(handleSave, 4000); // Save after 4 seconds of inactivity

    return () => {
      clearTimeout(saveTimeoutId);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editableNoteState, submit, note.status, isEditMode, hasEdited]);

  useEffect(() => {
    if (note.status === ProcessingStatus.Finalized) {
      setIsEditMode(false);
    }
  }, [note.status]);
  const handleEdit = () => setHasEdited(true);

  useEffect(() => {
    if (actionData?.actionType === "share-note") {
      if (actionData?.success) {
        setShareModalOpen(false);
        setShareDialogErrorMessage(null);
      } else {
        setShareDialogErrorMessage("Failed to share note");
      }
    }
  }, [
    actionData?.actionType,
    actionData?.success,
    actionData?.nonce,
    setShareModalOpen,
  ]);

  unstable_usePrompt({
    message: "Discard unsaved changes?",
    when: ({ currentLocation, nextLocation }) => {
      return (
        isEditMode &&
        enableManualSave &&
        currentLocation.pathname !== nextLocation.pathname
      );
    },
  });

  const hasAgendaData =
    interaction?.agenda?.data &&
    Object.hasOwn(interaction.agenda.data, "content") &&
    (interaction.agenda.data as { content: string }).content.length > 0;
  const hasAdvisorNotesData =
    interaction?.advisorNotes?.data &&
    Object.hasOwn(interaction.advisorNotes.data, "content") &&
    (interaction.advisorNotes.data as { content: string }).content.length > 0;

  const currentClient = editableNoteState.client
    ? {
        label: editableNoteState.client.name,
        value: editableNoteState.client.uuid,
      }
    : undefined;

  return (
    <SidebarV2
      favorSidebarOnMobile
      header={
        <HeaderV2
          left={
            <div className="lg:hidden">
              <BackButton
                to={location.state?.from ?? "/notes"}
                tooltip="Back"
              />
            </div>
          }
          right={
            note.status !== ProcessingStatus.Uploaded ? (
              <div className="flex flex-row items-center gap-2">
                {!enableLockdownMode && <EmailNoteButton noteId={note.uuid} />}
                <FollowUpEmailButton noteId={note.uuid} />
                <SyncToCrmButton
                  noteId={note.uuid}
                  disabled={note.status === ProcessingStatus.Finalized}
                  isClientSelectionEnabled={clientSelectionEnabled}
                  currentClient={currentClient}
                  dispatch={editableNoteDispatch}
                  options={getClientsForCombobox()}
                  clients={clientsWithCurrentClient}
                  handleSave={handleSave}
                  title="Sync to CRM"
                />

                {isEditMode && (
                  <DeleteButton onClick={openModal} tooltip="Delete Note" />
                )}
                {enableShareNote && (
                  <ShareButton onClick={() => setShareModalOpen(true)} />
                )}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={toggleEditMode}
                      disabled={note.status === ProcessingStatus.Finalized}
                      className={`flex items-center justify-center space-x-2 rounded-lg px-4 py-2 transition-transform duration-300 ${
                        note.status === ProcessingStatus.Finalized
                          ? "cursor-not-allowed bg-gray-400 text-gray-700"
                          : "bg-blue-500 text-white hover:bg-blue-600 active:bg-blue-700"
                      }`}
                      data-onboarding="edit-note-icon"
                    >
                      {isEditMode ? (
                        <PencilOffIcon className="!h-5 !w-5" />
                      ) : (
                        <PencilIcon className="!h-5 !w-5" />
                      )}
                      {isEditMode ? "Save" : "Edit"}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">
                    {isEditMode ? "Exit Edit Mode" : "Enter Edit Mode"}
                  </TooltipContent>
                </Tooltip>
              </div>
            ) : (
              <DeleteButton onClick={openModal} tooltip="Delete Note" />
            )
          }
        />
      }
    >
      <ShareModal
        isOpen={shareModalOpen}
        onClose={() => {
          setShareModalOpen(false);
          setShareDialogErrorMessage(null);
          setTimeout(
            () =>
              setAuthorizedUsers(
                (note.authorizedUserUuids ?? []).flatMap((uuid) => {
                  const userOption = userOptions.find(
                    (user) => user.uuid === uuid
                  );
                  return userOption ? [userOption] : [];
                })
              ),
            300
          );
        }}
        onChangeUsers={(authorizedUsers) => {
          setAuthorizedUsers(authorizedUsers);
          setShareDialogErrorMessage(null);
        }}
        onSave={() => {
          submit(
            {
              actionType: "share-note",
              authorizedUsers: authorizedUsers.map((user) => user.uuid),
            },
            {
              method: "post",
              encType: "application/json",
              replace: true,
            }
          );
        }}
        selected={authorizedUsers}
        userOptions={userOptions}
        errorMessage={shareDialogErrorMessage}
      />
      <ConfirmModal
        isOpen={isModalOpen}
        onClose={closeModal}
        onConfirm={confirmDelete}
        title="Confirm Delete"
        message="Are you sure you want to delete this note? This will delete the note for all users it's shared with."
      />

      <NotesOnboarding
        enableNoteSearch={enableNoteSearch}
        enableShareNote={enableShareNote}
        currentTab={currentTab}
      />

      <div className="flex w-full items-center justify-end">
        {enableNoteSearch && (
          <AskMeAnythingSection
            noteId={note.uuid}
            handleAddSectionToSummarySuccess={handleAddSectionToSummarySuccess}
            query={query}
            setQuery={setQuery}
            triggerSearch={triggerSearch}
            setTriggerSearch={setTriggerSearch}
          />
        )}
      </div>
      <div className="flex h-full flex-col items-center self-stretch">
        <div className="flex flex-col gap-3 self-stretch px-6 pb-6">
          <FormAlertsStack errors={formErrors} />
          <div className="flex items-center justify-between">
            <NoteEditableTitle
              dispatch={editableNoteDispatch}
              title={editableNoteState.meetingName}
              onEdit={() => setHasEdited(true)}
              disabled={
                note.status === ProcessingStatus.Finalized || !isEditMode
              }
            />
            {note.status === ProcessingStatus.Finalized && (
              <div className="flex items-center rounded-md bg-primary px-3 py-1 opacity-80 shadow-md">
                <Typography className="whitespace-nowrap text-sm text-white">
                  Note is finalized and cannot be edited.
                </Typography>
              </div>
            )}
          </div>
          {note.status === ProcessingStatus.Uploaded ? (
            <ContactSupportTab
              header="Note is being processed"
              body="This may take a moment. If you believe it's taking too long, please contact support for assistance."
            />
          ) : (
            <>
              <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
                {/* Tags */}
                {note.tags && note.tags.length > 0 && (
                  <div
                    className="flex items-start gap-2 lg:col-span-3"
                    data-onboarding="notes-keywords"
                  >
                    <TagIcon className="!h-6 !w-6 shrink-0 text-gray-500" />
                    <div className="flex flex-wrap gap-2">
                      {note.tags.map((tag, index) => (
                        <Badge
                          key={`a-${tag}`}
                          className={`${
                            index === 0 ? "inline-block" : ""
                          } max-w-fit break-words rounded-md bg-gray-100 p-1 text-xs text-gray-800`}
                        >
                          {capitalize(tag)}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Attendees Section */}
                <div className="lg:col-span-3">
                  {enableEditAttendees && isEditMode ? (
                    <AttendeesMultiSelect
                      initialOptions={attendeeOptions}
                      leftIcon={<PeopleOutlined />}
                      selected={editableNoteState.attendees}
                      emptyLabel={"Select attendees"}
                      onChange={(options) => {
                        if (!(options instanceof Array)) {
                          return;
                        }
                        editableNoteDispatch({
                          type: "updateAttendees",
                          nextAttendees: options,
                        });
                        setHasEdited(true);
                      }}
                    />
                  ) : (
                    <AttendeesSection
                      attendeesFromNote={note.attendees ?? []}
                      attendees={editableNoteState.attendees}
                    />
                  )}
                </div>

                {/* Created Time */}
                <div className="flex items-center text-gray-600 lg:col-span-1">
                  <ClockIcon className="mr-2 !h-6 !w-6 text-blue-500" />
                  <span className="text-sm font-medium">Created:</span>
                  <Typography
                    asChild
                    color="secondary"
                    variant="body2"
                    className="ml-2 text-sm"
                  >
                    <span>
                      {format(new Date(note.created), "ccc, MMM do, h:mm aaa")}
                    </span>
                  </Typography>
                </div>

                {/* Meeting Duration */}
                {note.meetingDurationSeconds > 0 && (
                  <div className="flex items-center text-gray-600 lg:col-span-1">
                    <FastForwardOutlined className="mr-2 h-5 w-5 text-green-500" />
                    <span className="text-sm font-medium">Duration:</span>
                    <Typography
                      asChild
                      color="secondary"
                      variant="body2"
                      className="ml-2 text-sm"
                    >
                      <span>
                        {secondsToDurationString(note.meetingDurationSeconds)}
                      </span>
                    </Typography>
                  </div>
                )}

                {/* CRM Client Selection */}
                <div className="lg:col-span-3">
                  {clientSelectionEnabled && (
                    <FormField id="clientId" name="clientId">
                      <FormLabel className="text-sm font-medium text-gray-700">
                        CRM Client
                      </FormLabel>
                      <CrmClientsDropdown
                        placeholder="Select a client"
                        leftIcon={
                          <OutboxOutlined className="h-5 w-5 text-gray-500" />
                        }
                        onChange={(optionType) => {
                          const nextValue = optionType?.value;
                          if (
                            isEditMode &&
                            note.status !== ProcessingStatus.Finalized
                          ) {
                            const clientToUpdate =
                              clientsWithCurrentClient.find(
                                ({ uuid }) => uuid === nextValue
                              ) ?? null;
                            editableNoteDispatch({
                              type: "updateClient",
                              client: clientToUpdate,
                            });
                            setHasEdited(true);
                          }
                        }}
                        disabled={
                          note.status === ProcessingStatus.Finalized ||
                          !isEditMode
                        }
                        selectedObject={currentClient}
                        searchOnLabel={true}
                      />
                    </FormField>
                  )}
                </div>
              </div>

              <Separator className="my-2" />

              <NoteTabGroup
                note={note}
                currentTab={currentTab}
                setCurrentTab={setCurrentTab}
                revalidator={revalidator}
                detailsTab={
                  <DetailsEditableTab
                    key={note.uuid}
                    dispatch={editableNoteDispatch}
                    actionItems={editableNoteState.actionItems}
                    advisorNotes={editableNoteState.advisorNotes}
                    keyTakeaways={editableNoteState.keyTakeaways}
                    disabled={
                      note.status === ProcessingStatus.Finalized || !isEditMode
                    }
                    onEdit={handleEdit}
                    isEditMode={isEditMode}
                    enableEditMode={enableEditMode}
                    isVisible={currentTab === "details"}
                  />
                }
                summaryTab={
                  <SummaryEditableTab
                    key={note.uuid}
                    dispatch={editableNoteDispatch}
                    summaryByTopics={editableNoteState.summaryByTopics}
                    disabled={
                      note.status === ProcessingStatus.Finalized || !isEditMode
                    }
                    onEdit={handleEdit}
                    activeIndex={currentSummaryIndex}
                    isVisible={currentTab === "summary"}
                    startSearch={startSearch}
                    isEditMode={isEditMode}
                  />
                }
                transcriptTab={
                  <TranscriptTab note={note} enableRemap={!isEditMode} />
                }
                prepTab={
                  (hasAgendaData || hasAdvisorNotesData) && (
                    <MeetingPrepTab
                      interaction={interaction}
                      clients={editableNoteState.attendees.filter(
                        (a) => a.type === "client"
                      )}
                      readOnly={true}
                    />
                  )
                }
                followUpTabs={(parsedFollowUps ?? [])
                  .map(({ followUp, parsedData }) => {
                    const tabDetails = {
                      title: followUp.title ?? `follow-up-${followUp.uuid}`,
                      status: followUp.status,
                      uuid: followUp.uuid,
                    };
                    if (followUp.status === FollowUpStatus.Processing) {
                      return {
                        ...tabDetails,
                        tab: (
                          <div className="flex items-center justify-center p-6">
                            <Skeleton className="h-64 w-full rounded-md" />
                          </div>
                        ),
                      };
                    }
                    if (
                      followUp.status === FollowUpStatus.Unknown ||
                      followUp.status === FollowUpStatus.Failed
                    ) {
                      return {
                        ...tabDetails,
                        tab: (
                          <ContactSupportTab
                            header="Issue processing meeting data"
                            body="We're looking into what happened here. If you need this data immediately, please contact support."
                          />
                        ),
                      };
                    }
                    if (
                      followUp.schema &&
                      "$id" in followUp.schema &&
                      followUp.schema.$id === numericalFollowUpDataSchemaID
                    ) {
                      return {
                        ...tabDetails,
                        tab: (
                          <NumericalDataTab
                            data={parsedData as NumericalData}
                          />
                        ),
                      };
                    } else if (
                      followUp.schema &&
                      "$id" in followUp.schema &&
                      followUp.schema.$id === structuredFollowUpDataSchemaID
                    ) {
                      return {
                        ...tabDetails,
                        tab: (
                          <StructuredDataReviewTab
                            followUpUUID={followUp.uuid}
                            noteUUID={note.uuid}
                            reviewData={parsedData as StructuredReviewData}
                            isEditable={isEditMode}
                          />
                        ),
                      };
                    }
                    return undefined;
                  })
                  .filter((f): f is FollowUpTab => f !== undefined)
                  .sort((a, b) => {
                    const titleComparison = a.title.localeCompare(b.title);
                    if (titleComparison !== 0) return titleComparison;
                    return a.uuid.localeCompare(b.uuid);
                  })}
              />
            </>
          )}
        </div>
      </div>
    </SidebarV2>
  );
};

export default Route;
