import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { SyncToCrmButton, SyncToCrmCombinedProps } from "./SyncToCrmButton";

// Mock react-toastify
vi.mock("react-toastify", () => ({
  toast: {
    loading: vi.fn(() => "toast-id"),
    success: vi.fn(),
    error: vi.fn(),
    dismiss: vi.fn(),
  },
}));

// Mock CrmClientsDropdown
vi.mock("./components/CrmClientsDropdown", () => ({
  default: ({ onChange, selectedObject, placeholder }: any) => (
    <div data-testid="crm-clients-dropdown">
      <button
        onClick={() => onChange({ value: "client-1", label: "Test Client" })}
      >
        {selectedObject?.label || placeholder}
      </button>
    </div>
  ),
}));

// Mock Remix hooks
vi.mock("@remix-run/react", () => ({
  useFetcher: () => ({
    submit: vi.fn(),
    data: null,
    state: "idle",
  }),
  useNavigation: () => ({
    state: "idle",
    location: undefined,
    formMethod: undefined,
    formAction: undefined,
    formEncType: undefined,
    formData: undefined,
  }),
  useActionData: () => undefined,
}));

// Mock shadcn UI components
vi.mock("~/@shadcn/ui/button", () => ({
  Button: ({ children, onClick, disabled, ...props }: any) => (
    <button onClick={onClick} disabled={disabled} {...props}>
      {children}
    </button>
  ),
}));

vi.mock("~/@shadcn/ui/tooltip", () => ({
  TooltipProvider: ({ children }: any) => <div>{children}</div>,
  Tooltip: ({ children }: any) => <div>{children}</div>,
  TooltipTrigger: ({ children }: any) => children,
  TooltipContent: ({ children }: any) => <div>{children}</div>,
}));

vi.mock("~/@shadcn/ui/dialog", () => ({
  Dialog: ({ children, open }: any) => open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: any) => <div data-testid="dialog-content">{children}</div>,
  DialogHeader: ({ children }: any) => <div data-testid="dialog-header">{children}</div>,
  DialogTitle: ({ children }: any) => <h2>{children}</h2>,
  DialogDescription: ({ children }: any) => <p>{children}</p>,
  DialogFooter: ({ children }: any) => <div data-testid="dialog-footer">{children}</div>,
}));

vi.mock("~/@shadcn/ui/checkbox", () => ({
  Checkbox: ({ checked, onCheckedChange }: any) => (
    <input
      type="checkbox"
      checked={checked}
      onChange={(e) => onCheckedChange?.(e.target.checked)}
    />
  ),
}));

describe("SyncToCrmButton", () => {
  const mockHandleSave = vi.fn();

  const defaultProps: SyncToCrmCombinedProps = {
    noteId: "test-note-id",
    disabled: false,
    handleSave: mockHandleSave,
  };

  const mockClients = [
    { uuid: "client-1", name: "Test Client", type: "individual" },
    { uuid: "client-2", name: "Another Client", type: "business" },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("renders sync button", () => {
    render(<SyncToCrmButton {...defaultProps} />);

    const button = screen.getByRole("button");
    expect(button).toBeInTheDocument();
    expect(button).toHaveAttribute("aria-label", "Sync to CRM");
  });

  it("renders with title when provided", () => {
    render(<SyncToCrmButton {...defaultProps} title="Sync Note" />);

    expect(screen.getByText("Sync Note")).toBeInTheDocument();
  });

  it("renders disabled state correctly", () => {
    render(<SyncToCrmButton {...defaultProps} disabled />);

    const button = screen.getByRole("button");
    expect(button).toBeDisabled();
  });

  it("renders with client selection enabled", () => {
    const mockDispatch = vi.fn();
    render(
      <SyncToCrmButton
        {...defaultProps}
        isClientSelectionEnabled
        clients={mockClients}
        dispatch={mockDispatch}
      />
    );

    const button = screen.getByRole("button");
    expect(button).toBeInTheDocument();
  });

  it("renders with current client", () => {
    const mockDispatch = vi.fn();
    render(
      <SyncToCrmButton
        {...defaultProps}
        isClientSelectionEnabled
        clients={mockClients}
        dispatch={mockDispatch}
        currentClient={{ value: "client-1", label: "Test Client" }}
      />
    );

    const button = screen.getByRole("button");
    expect(button).toBeInTheDocument();
  });

  it("applies custom variant and size props", () => {
    render(
      <SyncToCrmButton
        {...defaultProps}
        variant="outline"
        size="default"
      />
    );

    const button = screen.getByRole("button");
    expect(button).toBeInTheDocument();
  });

  it("handles component props correctly", () => {
    render(
      <SyncToCrmButton
        {...defaultProps}
        title="Custom Title"
      />
    );

    const button = screen.getByRole("button");
    expect(button).toBeInTheDocument();
    expect(screen.getByText("Custom Title")).toBeInTheDocument();
  });

  it("renders without crashing with minimal props", () => {
    render(
      <SyncToCrmButton
        noteId="test-note"
        disabled={false}
        handleSave={mockHandleSave}
      />
    );

    const button = screen.getByRole("button");
    expect(button).toBeInTheDocument();
  });
});