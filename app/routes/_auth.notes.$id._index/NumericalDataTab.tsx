import { ContentCopy } from "@mui/icons-material";
import React from "react";
import { toast } from "react-toastify";

// Type definitions for schema
type TableColumn = {
  id: string;
  caption: string;
  data_type: string;
};

type TableDataItem = {
  values: Record<string, any>;
  format: {
    decimal_places?: number;
    show_symbol?: boolean;
    date_format?: string;
    value_type: "number" | "percentage" | "currency" | "date";
  };
  evidence?: { quote: string; timestamp?: string }[];
};

type NumericalData = {
  table_definition: {
    title?: string;
    description?: string;
    columns: TableColumn[];
  };
  data: TableDataItem[];
};

export const NumericalDataTab: React.FC<{ data: NumericalData }> = ({
  data,
}) => {
  const { table_definition, data: rows } = data;

  const formatValue = (value: any, format: TableDataItem["format"]) => {
    if (value === null || value === undefined) return "-";

    const numericValue = typeof value === "number" ? value : Number(value);
    const isNumeric = !isNaN(numericValue);

    const getLocaleOptions = () => {
      const options: Intl.NumberFormatOptions = {};
      if (format.decimal_places !== undefined) {
        options.minimumFractionDigits = format.decimal_places;
        options.maximumFractionDigits = format.decimal_places;
      }
      return options;
    };

    switch (format.value_type) {
      case "number":
        return isNumeric
          ? numericValue.toLocaleString("en-US", getLocaleOptions())
          : value;
      case "percentage":
        return isNumeric
          ? `${numericValue.toLocaleString("en-US", getLocaleOptions())}%`
          : value;
      case "currency":
        if (isNumeric) {
          const formattedNumber = numericValue.toLocaleString(
            "en-US",
            getLocaleOptions()
          );
          return format.show_symbol ? `$${formattedNumber}` : formattedNumber;
        }
        return value;
      case "date":
        try {
          const dateStyleMap: Record<
            string,
            Intl.DateTimeFormatOptions["dateStyle"]
          > = {
            full: "full",
            long: "long",
            medium: "medium",
            short: "short",
          };
          const dateStyle =
            dateStyleMap[format.date_format || "medium"] || "medium";
          return new Date(value).toLocaleDateString("en-US", { dateStyle });
        } catch (error) {
          return value;
        }
      default:
        return value;
    }
  };

  const copySingleNumericValue = (
    value: any,
    format: TableDataItem["format"]
  ) => {
    const formattedValue = formatValue(value, format);
    navigator.clipboard.writeText(formattedValue).then(() => {
      toast.success(`Data copied successfully!`, {
        position: "top-right",
        autoClose: 2000,
      });
    });
  };

  return (
    <div className="overflow-x-auto">
      <h2 className="text-xl font-bold">{table_definition.title}</h2>
      <p className="text-gray-600">{table_definition.description}</p>
      <table className="mt-4 min-w-full border-collapse border border-gray-300">
        <thead>
          <tr>
            {table_definition.columns.map((col) => (
              <th
                key={col.id}
                className="border border-gray-300 px-4 py-2 text-left font-medium text-gray-700"
              >
                {col.caption}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {rows.map((row, rowIndex) => (
            <tr key={rowIndex} className="border-t">
              {table_definition.columns.map((col) => (
                <td
                  key={col.id}
                  className="border border-gray-300 px-4 py-2 text-gray-700"
                >
                  {col.data_type === "number" ? (
                    <div className="flex items-center justify-end space-x-1">
                      <span>{formatValue(row.values[col.id], row.format)}</span>
                      <button
                        onClick={() =>
                          copySingleNumericValue(row.values[col.id], row.format)
                        }
                        className="p-1 text-blue-500 hover:text-blue-700 focus:outline-none"
                        title="Copy value"
                      >
                        <ContentCopy fontSize="small" />
                      </button>
                    </div>
                  ) : (
                    formatValue(row.values[col.id], row.format)
                  )}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
