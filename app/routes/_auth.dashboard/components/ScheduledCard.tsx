import { <PERSON>, useFetcher, useLocation } from "@remix-run/react";
import { <PERSON>Dot, CircleHelp } from "lucide-react";

import { Attendee } from "~/utils/notesUtils";
import MeetingLinkButton from "./MeetingLinkButton";
import {
  GenerateAgendaEmailButton,
  OpenMeetingPrepButton,
  StartMeetingButton,
} from "./buttons";
import { ClientInteraction, ProcessingStatus } from "~/api/openapi/generated";
import { AttendeesList } from "./AttendeesList";
import { cn } from "~/@shadcn/utils";
import { useFlag } from "~/context/flags";
import { Switch } from "~/@shadcn/ui/switch";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { action } from "~/routes/_auth.dashboard/route";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";

interface ScheduledCardProps {
  meetingName: string;
  scheduledStartTime: Date;
  scheduledEndTime?: Date;
  attendees: Attendee[];
  interaction?: ClientInteraction;
  meetingType?: string | null;
  status: ProcessingStatus;
  active?: boolean;
  meetingLink?: string;
  toPrep: { pathname: string; search?: string };
  toMeeting: { pathname: string; search?: string };
  useDesktopLayout: boolean;
  id?: string;
  prepButtonId?: string;
  autojoinEnabled?: boolean;
  autojoinAvailable?: boolean;
  scheduledEventUUID?: string;
}

export const MeetingTime = ({
  startTime,
  endTime,
  className,
}: {
  startTime: Date;
  endTime?: Date;
  className?: string;
}) => {
  return (
    <p
      className={cn(
        "min-w-0 break-words text-sm font-normal text-blue-900",
        className
      )}
    >
      {endTime
        ? `${startTime.toLocaleDateString(undefined, {
            month: "numeric",
            day: "numeric",
          })}, ${startTime.toLocaleTimeString(undefined, {
            hour: "numeric",
            minute: "2-digit",
          })} - ${endTime.toLocaleTimeString(undefined, {
            hour: "numeric",
            minute: "2-digit",
          })}`
        : `${startTime.toLocaleDateString()}, ${startTime.toLocaleTimeString(
            undefined,
            {
              hour: "numeric",
              minute: "2-digit",
            }
          )}`}
    </p>
  );
};

const ScheduledCard = ({
  meetingName,
  scheduledStartTime,
  attendees,
  meetingType,
  status,
  toPrep,
  toMeeting,
  scheduledEndTime,
  active,
  meetingLink,
  interaction,
  useDesktopLayout,
  id,
  prepButtonId,
  autojoinEnabled,
  autojoinAvailable,
  scheduledEventUUID,
}: ScheduledCardProps) => {
  const location = useLocation();
  const isMeetingPrepEnabled = useFlag(
    "EnableClientIntelligencePreMeetingWorkflow"
  );

  const fetcher = useFetcher<typeof action>();
  const [autojoinChecked, setAutojoinChecked] = useState(autojoinEnabled);
  const updateAutoJoin = (checked: boolean) => {
    const formData = new FormData();
    formData.append("actionType", "update-auto-join");
    if (scheduledEventUUID) {
      formData.append("scheduledEventUUID", scheduledEventUUID);
    }
    formData.append("autojoinEnabled", checked.toString());
    fetcher.submit(formData, {
      method: "post",
      encType: "multipart/form-data",
    });
    toast.loading("Updating auto-join", {
      position: "top-center",
      autoClose: 2000,
      hideProgressBar: true,
      closeOnClick: true,
      progress: undefined,
      toastId: "update-autojoin",
    });
  };

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (fetcher.data.success) {
        toast.update("update-autojoin", {
          render: "Successfully updated auto-join",
          type: toast.TYPE.SUCCESS,
          isLoading: false,
          autoClose: 2000,
        });
      } else {
        setAutojoinChecked(!autojoinChecked);
        toast.update("update-autojoin", {
          render: "Failed to update auto-join",
          type: toast.TYPE.ERROR,
          isLoading: false,
          autoClose: 2000,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetcher.state, fetcher.data]);

  const autojoinToggleIfAutojoinAvailable = autojoinAvailable && (
    <div className="flex items-center gap-2">
      <Tooltip>
        <TooltipTrigger asChild>
          <label className="flex cursor-help items-center gap-1">
            Auto-join
            <CircleHelp className="h-5 w-5" />
          </label>
        </TooltipTrigger>
        <TooltipContent className="max-w-[300px] whitespace-normal text-sm">
          Toggling this on will ensure that a bot is sent to this meeting
          automatically; a bot may still join the meeting automatically if this
          is toggled off if another user has it toggled on.
        </TooltipContent>
      </Tooltip>
      <Switch
        checked={autojoinChecked}
        onCheckedChange={(checked) => {
          setAutojoinChecked(checked);
          updateAutoJoin(checked);
        }}
      />
    </div>
  );

  return (
    <div
      id={id}
      className={`flex w-full flex-col gap-2 rounded-2xl ${
        active ? "border-[3px] border-[#B90000]" : "border"
      } px-2 py-1 sm:px-4 sm:py-4`}
    >
      <div className="flex w-full min-w-0 flex-col items-start justify-between gap-4 break-words sm:flex-row sm:gap-0">
        <div className="flex w-full min-w-0 flex-col items-start gap-2">
          {active && <BellDot className="text-[#B90000]" />}
          <Link
            className="text-md w-full min-w-0 font-semibold text-[#182349] sm:w-auto sm:text-xl sm:underline"
            to={toMeeting}
            state={{ from: `${location.pathname}${location.search}` }}
          >
            {meetingName}
            {!useDesktopLayout && (
              <MeetingTime
                className="mt-2"
                startTime={scheduledStartTime}
                endTime={scheduledEndTime}
              />
            )}
          </Link>
        </div>
        <div className="flex flex-none flex-row-reverse items-center gap-2 md:flex-row">
          {!isMeetingPrepEnabled ? (
            <StartMeetingButton to={toMeeting} />
          ) : interaction?.uuid ? (
            <>
              <OpenMeetingPrepButton
                title="Meeting Prep"
                to={toPrep}
                variant="ghost"
                id={prepButtonId}
              />
              <GenerateAgendaEmailButton
                agenda={interaction?.agenda}
                emailSubject={`Agenda for our meeting on ${scheduledStartTime.toLocaleDateString()}`}
                size="default"
                title="Generate Agenda Email"
              />
              <StartMeetingButton to={toMeeting} />
            </>
          ) : (
            <OpenMeetingPrepButton
              title="Meeting Prep"
              to={toPrep}
              id={prepButtonId}
            />
          )}
        </div>
      </div>

      {status === ProcessingStatus.Scheduled &&
        (useDesktopLayout ? (
          <>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <MeetingLinkButton
                  meetingLink={meetingLink || toMeeting}
                  shouldLinkToMeeting={true}
                />
                <MeetingTime
                  startTime={scheduledStartTime}
                  endTime={scheduledEndTime}
                />
              </div>
              {autojoinToggleIfAutojoinAvailable}
            </div>

            <div className="flex items-center justify-between">
              <AttendeesList attendees={attendees} className="mx-1" />
              <p className="text-base text-[#213CA1]">{meetingType || ""}</p>
            </div>
          </>
        ) : (
          autojoinToggleIfAutojoinAvailable
        ))}
    </div>
  );
};

export default ScheduledCard;
