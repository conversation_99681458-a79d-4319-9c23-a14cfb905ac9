import { LoaderFunctionArgs, redirect } from "@remix-run/node";
import { setUpOAuthProvider } from "~/api/oauth/setUpOAuthProvider.server";
import { OAuthRequestProviderEnum } from "~/api/openapi/generated";
import { logError } from "~/utils/log.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const authorizationCode = new URL(request.url).searchParams.get("code");
  if (!authorizationCode) {
    throw new Error("Missing authorization_code query parameter");
  }
  try {
    await setUpOAuthProvider({
      authorizationCode,
      provider: OAuthRequestProviderEnum.Wealthbox,
      request,
    });
    return redirect("/settings?integrationStatus=true&integration=WealthBox");
  } catch (error) {
    logError("Failed to setup WealthBox integration", error);
    return redirect("/settings?integrationStatus=false&integration=WealthBox");
  }
};
