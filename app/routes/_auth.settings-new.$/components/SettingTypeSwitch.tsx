import { useState } from "react";

import { SectionItemBooleanField } from "~/api/openapi/generated";
import { Switch } from "~/@shadcn/ui/switch";

type SettingTypeSwitchProps = SectionItemBooleanField & {
  onChange: (id: string, value: boolean) => void;
};

const SettingTypeSwitch = ({
  id,
  label,
  value,
  onChange,
}: SettingTypeSwitchProps) => {
  const [isEnabled, setIsEnabled] = useState(!!value);
  return (
    <div className="mt-6 flex items-center">
      <label htmlFor={id}>{label}</label>
      <Switch
        id={id}
        checked={isEnabled}
        onCheckedChange={() => {
          setIsEnabled(!isEnabled);
          onChange(id, !isEnabled);
        }}
        className="ml-2"
      />
    </div>
  );
};

export default SettingTypeSwitch;
