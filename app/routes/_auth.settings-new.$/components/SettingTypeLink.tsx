import { SectionItemLink } from "~/api/openapi/generated";
import { Button } from "~/@shadcn/ui/button";
import { INTERCOM_LAUNCHER_SELECTOR } from "~/third-party/Intercom/Intercom";

type TextFieldProps = SectionItemLink;
const Link = ({ id, label, description, text, appTag }: TextFieldProps) => {
  let button = null;

  switch (appTag) {
    case "contact-support":
      button = (
        <Button className="mt-3" size="default" id={INTERCOM_LAUNCHER_SELECTOR}>
          <span>{text}</span>
        </Button>
      );
      break;
    default:
      button = (
        <Button className="mt-3" size="default">
          <span>{text}</span>
        </Button>
      );
      break;
  }

  return (
    <div className="mt-6">
      {label && (
        <label htmlFor={id}>
          <h3 className="font-medium">{label}</h3>
        </label>
      )}
      {description && <p className="mt-3">{description}</p>}
      {button}
    </div>
  );
};

export default Link;
