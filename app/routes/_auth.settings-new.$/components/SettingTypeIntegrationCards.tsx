import { useState } from "react";

import { cn } from "~/@shadcn/utils";
import {
  SectionItemIntegrationCard,
  LabeledEntity,
  SectionItemIntegrationCards,
} from "~/api/openapi/generated";

import { GoogleCalendarIcon } from "~/@ui/assets/GoogleCalendarIcon";
import { GoogleMeetIcon } from "~/@ui/assets/GoogleMeetIcon";
import { MicrosoftTeamsIcon } from "~/@ui/assets/MicrosoftTeamsIcon";
import { OutlookIcon } from "~/@ui/assets/OutlookIcon";
import { RedtailLogo } from "~/@ui/assets/RedtailLogo";
import { SalesforceIcon } from "~/@ui/assets/SalesforceIcon";
import { WealthboxIcon } from "~/@ui/assets/WealthboxIcon";
import { WebexIcon } from "~/@ui/assets/WebexIcon";
import { ZoomIcon } from "~/@ui/assets/ZoomIcon";
import { Button } from "~/@shadcn/ui/button";

const IntegrationCards = ({
  id,
  label,
  filters,
  cards,
}: SectionItemIntegrationCards) => {
  const [activeFilter, setActiveFilter] = useState("");

  const handleSelectFilter = (filterId: string) => {
    setActiveFilter(filterId);
  };

  const filteredCards = activeFilter
    ? cards?.filter((card) => card.tag === activeFilter)
    : cards;

  const updatedFilters = filters ? [{ id: "", label: "All" }, ...filters] : [];

  return (
    <div className="mt-6" id={id}>
      <div className="flex flex-col items-start justify-between lg:flex-row lg:items-center">
        <h3 className="text-lg font-bold">{label}</h3>
        <FilterBar
          filters={updatedFilters}
          selectedId={activeFilter}
          onSelectFilter={handleSelectFilter}
        />
      </div>
      <div className="flex overflow-auto sm:flex-wrap">
        {filteredCards?.map((card) => (
          <IntegrationCard key={card.id} data={card} />
        ))}
        {!filteredCards?.length && (
          <span className="mt-4">No data exists for the current filter</span>
        )}
      </div>
    </div>
  );
};

type FilterBarProps = {
  filters: LabeledEntity[];
  selectedId: string;
  onSelectFilter: (filterId: string) => void;
};
const FilterBar = ({ filters, selectedId, onSelectFilter }: FilterBarProps) => {
  return (
    <div className="mt-2 flex max-w-full items-center lg:mt-0">
      <span className="font-semibold">Show</span>
      <div className="ml-3 flex overflow-auto rounded-md border border-gray-200 p-1">
        {filters.map((filter) => (
          <button
            key={filter.id}
            className={cn(
              "whitespace-nowrap border border-transparent px-2 py-1 text-sm font-medium",
              selectedId === filter.id &&
                "rounded-sm border-primary bg-primary-foreground"
            )}
            onClick={() => onSelectFilter(filter.id)}
          >
            {filter.label}
          </button>
        ))}
      </div>
    </div>
  );
};

function getIntegrationIcon(id: string) {
  switch (id) {
    case "connect_ms_calendar":
      return OutlookIcon;
    case "connect_microsoft_teams":
      return MicrosoftTeamsIcon;
    case "connect_salesforce_contact_support":
      return SalesforceIcon;
    case "connect_wealthbox":
      return WealthboxIcon;
    case "connect_zoom":
      return ZoomIcon;
    case "connect_google_calendar":
      return GoogleCalendarIcon;
    case "connect_google_meet":
      return GoogleMeetIcon;
    case "connect_redtail":
      return RedtailLogo;
    case "connect_webex":
      return WebexIcon;
    default:
      return () => null;
  }
}
type IntegrationCardType = {
  data: SectionItemIntegrationCard;
};
const IntegrationCard = ({ data }: IntegrationCardType) => {
  const { id, label, value, isActive, isInteractible, redirectPath } = data;

  let ctaText = value;
  if (!ctaText) {
    ctaText = (isActive ? "Reconnect" : "Connect") + " " + label;
  }

  const Icon = getIntegrationIcon(id);

  return (
    <div
      className={cn(
        "mr-5 mt-5 w-64 shrink-0 rounded-lg border p-6 last-of-type:mr-0",
        isActive && "border-primary"
      )}
    >
      <div className="flex items-center justify-between">
        <h4>{label}</h4>
        <Icon className="h-6 w-6" />
      </div>
      <div className="mt-2 text-sm text-muted-foreground">
        {isActive ? "Integrated" : "Not integrated"}
      </div>
      {isInteractible && (
        <Button
          variant={isActive ? "outline" : "outline_primary"}
          className={cn("mt-6 w-full text-sm")}
          onClick={() => {
            if (!redirectPath) {
              return;
            }
            window.location.href = redirectPath;
          }}
        >
          {ctaText}
        </Button>
      )}
    </div>
  );
};

export default IntegrationCards;
