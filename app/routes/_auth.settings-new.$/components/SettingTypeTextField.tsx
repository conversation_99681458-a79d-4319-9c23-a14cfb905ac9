import { SectionItemTextField } from "~/api/openapi/generated";

type TextFieldProps = SectionItemTextField & {
  onChange: (id: string, value: string) => void;
};
const TextField = ({
  id,
  label,
  onChange,
  placeholder,
  value,
  disabled,
}: TextFieldProps) => {
  return (
    <div className="mt-6 flex flex-col">
      <label htmlFor={id}>
        <h3 className="font-medium">{label}</h3>
      </label>
      <input
        type="text"
        value={value || ""}
        id={id}
        disabled={disabled || false}
        onChange={(e) => onChange(id, e.target.value)}
        placeholder={placeholder || ""}
        className={`mt-3 h-10 w-80 rounded-md border px-3 ${
          disabled ? "opacity-50" : ""
        }`}
      />
    </div>
  );
};

export default TextField;
