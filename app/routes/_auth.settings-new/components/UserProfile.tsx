import { AccountCircle } from "@mui/icons-material";
import { Typography } from "~/@ui/Typography";

const UserProfile = ({
  firstName,
  lastName,
  email,
}: {
  firstName: string;
  lastName: string;
  email: string;
}) => {
  return (
    <>
      <div className="flex flex-row items-center p-2">
        <AccountCircle className="!h-12 !w-12 opacity-50" />
        <div className="ml-4 flex flex-col">
          <Typography variant="h2">
            {firstName} {lastName}
          </Typography>
          <Typography className="text-md" variant="body1">
            {email}
          </Typography>
        </div>
      </div>
    </>
  );
};

export default UserProfile;
