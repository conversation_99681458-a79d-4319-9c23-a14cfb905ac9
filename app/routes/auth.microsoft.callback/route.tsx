import { LoaderFunctionArgs, redirect } from "@remix-run/node";
import { setUpOAuthProvider } from "~/api/oauth/setUpOAuthProvider.server";
import { OAuthRequestProviderEnum } from "~/api/openapi/generated";
import { MICROSOFT_STRATEGY, authenticator } from "~/auth/authenticator.server";
import { logError } from "~/utils/log.server";

// Exports
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URL(request.url).searchParams;
  if (searchParams.get("state") !== "calendar_integration") {
    await authenticator.authenticate(MICROSOFT_STRATEGY, request, {
      successRedirect: "/",
      failureRedirect: "/auth/login",
    });
    return null;
  }

  const authorizationCode = searchParams.get("code");
  if (!authorizationCode) {
    throw new Error("Missing authorization_code query parameter");
  }

  try {
    await setUpOAuthProvider({
      authorizationCode,
      provider: OAuthRequestProviderEnum.Microsoft,
      request,
    });
    return redirect("/settings?integrationStatus=true&integration=Calendar");
  } catch (error) {
    logError("Failed to setup calendar integration", error);
    return redirect("/settings?integrationStatus=false&integration=Calendar");
  }
};
