// NOTE: This component is a copy of the one at app/routes/_auth.settings._index/Sections/components/UserImpersonation.tsx.
// The latter will be removed along with settings v1 & v2

// TODO: @debo<PERSON><PERSON><PERSON><PERSON><PERSON> Refactor this, and improve UX

import { AuthorizationError } from "remix-auth";
import { ActionFunctionArgs, json } from "@remix-run/node";
import { Link, useFetcher } from "@remix-run/react";
import { z } from "zod";
import { useState } from "react";

import { Button } from "~/@shadcn/ui/button";
import { FormField, FormLabel } from "~/@shadcn/ui/form";
import { Input } from "~/@shadcn/ui/input";
import {
  authenticator,
  IMPERSONATE_STRATEGY,
} from "~/auth/authenticator.server";
import { flattenZodErrors } from "~/utils/validation";
import { logError } from "~/utils/log.server";
import { ArrowBackOutlined } from "@mui/icons-material";

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    // Clone the request to allow for multiple reads of the body since in the case of user impersonation we need to read the FormData again
    const cloned_request = request.clone();
    const formData = await request.formData();
    const id = formData.get("id") as string;

    if (!id) {
      throw new Error("Required parameter 'id' is missing or undefined.");
    }

    try {
      return authenticator.authenticate(IMPERSONATE_STRATEGY, cloned_request, {
        successRedirect: "/",
        throwOnError: true,
        context: { request: cloned_request },
      });
    } catch (error) {
      // authenticator.authenticate redirects using thrown a Response, allow it to
      // bubble up
      if (error instanceof Response) throw error;

      // Auth service throws AuthorizationErrors
      if (error instanceof AuthorizationError) {
        if (error.cause instanceof z.ZodError) {
          return json(
            { errors: flattenZodErrors(error.cause) },
            { status: 400 }
          );
        }
        return json({ errors: [error.cause?.message] }, { status: 400 });
      }
    }

    return { success: true };
  } catch (e) {
    logError("!!! api/settings.action", e);
    return { success: false };
  }
};

const UserImpersonation = ({
  isModalOpen,
  setIsModalOpen,
}: {
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [email, setEmail] = useState("");
  const [purpose, setPurpose] = useState("");
  const [ttlSeconds, setTtlSeconds] = useState("600");

  const fetcher = useFetcher();

  const handleSubmit = () => {
    if (email === "") {
      alert("Email is required");
      return;
    }
    if (purpose === "") {
      alert("Purpose is required");
      return;
    }

    const formData = new FormData();
    formData.append("id", "user-impersonate");
    formData.append("type", "user-impersonate");
    formData.append("email", email);
    formData.append("ttlSeconds", ttlSeconds);
    formData.append("purpose", purpose);

    fetcher.submit(formData, {
      method: "post",
      action: "/settings",
      encType: "multipart/form-data",
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center">
        <Button className="mr-2 md:hidden" size="icon-sm" variant="outline">
          <Link to="/settings-new/integrations">
            <ArrowBackOutlined />
          </Link>
        </Button>
        <h2 className="text-2xl font-semibold">User Impersonation</h2>
      </div>

      <FormField name="Email" className="my-2">
        <FormLabel>Email Address</FormLabel>
        <Input
          value={email}
          onChange={(e) => {
            setEmail(e.target.value);
          }}
        />
      </FormField>
      <FormField name="Purpose">
        <FormLabel>Purpose</FormLabel>
        <Input
          type="text-box"
          value={purpose}
          onChange={(e) => {
            setPurpose(e.target.value);
          }}
        />
      </FormField>
      <FormField name="Time To Live">
        <FormLabel>{`Time To Live(in seconds)`}</FormLabel>
        <Input
          type="text-box"
          value={ttlSeconds}
          onChange={(e) => {
            setTtlSeconds(e.target.value);
          }}
        />
      </FormField>

      <Button onClick={handleSubmit}>Impersonate</Button>
    </div>
  );
};

export default UserImpersonation;
