import { Link } from "@remix-run/react";
import { Suspense } from "react";
import { But<PERSON> } from "~/@shadcn/ui/button";
import { Card, CardContent } from "~/@shadcn/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/@shadcn/ui/select";
import { Switch } from "~/@shadcn/ui/switch";
import { GoogleCalendarIcon } from "~/@ui/assets/GoogleCalendarIcon";
import { GoogleMeetIcon } from "~/@ui/assets/GoogleMeetIcon";
import { MicrosoftTeamsIcon } from "~/@ui/assets/MicrosoftTeamsIcon";
import { OutlookIcon } from "~/@ui/assets/OutlookIcon";
import { RedtailLogo } from "~/@ui/assets/RedtailLogo";
import { SalesforceIcon } from "~/@ui/assets/SalesforceIcon";
import { WealthboxIcon } from "~/@ui/assets/WealthboxIcon";
import { WebexIcon } from "~/@ui/assets/WebexIcon";
import { ZoomIcon } from "~/@ui/assets/ZoomIcon";
import { Typography } from "~/@ui/Typography";
import { Setting } from "~/api/openapi/generated";
import * as all from "@mui/icons-material";

type DynamicIconProps = {
  iconName:
    | keyof typeof all
    | "ZoomIcon"
    | "GoogleCalendarIcon"
    | "RedtailLogo"
    | "WealthboxIcon"
    | "SalesforceIcon"
    | "WebexIcon"
    | "GoogleMeetIcon"
    | "MicrosoftTeamsIcon"
    | "OutlookIcon";
};

const DynamicIcon: React.FC<DynamicIconProps> = ({ iconName }) => {
  const muiIcon = all[iconName as keyof typeof all];

  if (muiIcon) {
    const IconComponent = muiIcon;
    return (
      <Suspense fallback={<div>Loading...</div>}>
        <IconComponent className="h-6 w-6" />
      </Suspense>
    );
  } else {
    const CustomIconMap = {
      ZoomIcon,
      GoogleCalendarIcon,
      RedtailLogo,
      WealthboxIcon,
      SalesforceIcon,
      WebexIcon,
      GoogleMeetIcon,
      MicrosoftTeamsIcon,
      OutlookIcon,
    };

    const CustomIconComponent =
      CustomIconMap[iconName as keyof typeof CustomIconMap];

    return (
      <Suspense fallback={<div>Loading...</div>}>
        <CustomIconComponent className="h-6 w-6" />
      </Suspense>
    );
  }
};

const SettingCard = ({ setting }: { setting: Setting }) => {
  return (
    <Card>
      <CardContent className="p-2">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-lg font-semibold">{setting.title}</span>
            <DynamicIcon
              iconName={setting.icon as DynamicIconProps["iconName"]}
            />
          </div>
          <p className="text-sm text-muted-foreground">
            {setting.isActive ? "Integrated" : "Not integrated"}
          </p>
          {setting.editable && setting.type !== "info" && (
            <Link to={setting.redirectPath ?? ""}>
              <Button
                variant="outline"
                className="h-auto w-full whitespace-normal"
              >
                <Typography variant={"body2"}>
                  {setting.isActive ? "Reconnect" : "Connect"} {setting.title}
                </Typography>
              </Button>
            </Link>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ({
  setting,
  handleToggleChange,
  handleSelectChange,
  updatableSettingsState,
}: {
  setting: Setting;
  handleToggleChange: Function;
  handleSelectChange: Function;
  updatableSettingsState: { [id: string]: any };
}) => {
  return (
    <div>
      {setting.type === "toggle" && (
        <Typography className="inline-flex items-center rounded-md px-2 py-2">
          <span className="mx-1 grow">{setting.title}</span>
          {setting.value && (
            <Typography asChild color="secondary" variant="body2">
              <span className="mx-2">{setting.value}</span>
            </Typography>
          )}
          <Switch
            checked={updatableSettingsState[setting.id]}
            disabled={!(setting.editable ?? true)}
            onCheckedChange={(checked) =>
              handleToggleChange(setting.id, checked)
            }
          />
        </Typography>
      )}
      {setting.type === "select" && (
        <Typography className="inline-flex items-center rounded-md px-2 py-2">
          <span className="mx-1 grow">{setting.title}</span>
          <Select
            value={updatableSettingsState[setting.id]}
            onValueChange={(nextValue) => {
              if (typeof nextValue !== "string" && nextValue !== undefined) {
                return;
              }
              handleSelectChange(setting.id, nextValue);
            }}
          >
            <SelectTrigger className="w-auto rounded-md border-gray-300 text-sm shadow-sm">
              <SelectValue placeholder="Select a time window" />
            </SelectTrigger>
            <SelectContent>
              {setting.options?.map((option) => (
                <SelectItem
                  className={"flex-basis:auto grow-0"}
                  key={option.value}
                  value={option.value}
                >
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </Typography>
      )}
      {setting.type === "link" && <SettingCard setting={setting} />}
      {setting.type === "routerLink" && <SettingCard setting={setting} />}
      {setting.type === "info" && <SettingCard setting={setting} />}
    </div>
  );
};
