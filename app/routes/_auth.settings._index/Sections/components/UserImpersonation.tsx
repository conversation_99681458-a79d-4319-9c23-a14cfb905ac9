import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogHeader } from "~/@shadcn/ui/dialog";
import { Button } from "~/@shadcn/ui/button";
import { FormField, FormLabel } from "~/@shadcn/ui/form";
import { Input } from "~/@shadcn/ui/input";
import { SettingsSection } from "~/api/openapi/generated";
import { sidebarOptions } from "../../route";
import { useFetcher } from "@remix-run/react";

const ImpersonationModal = ({
  isModalOpen,
  setIsModalOpen,
}: {
  isModalOpen: boolean;
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [email, setEmail] = useState("");
  const [purpose, setPurpose] = useState("");
  const [ttlSeconds, setTtlSeconds] = useState("600");

  const fetcher = useFetcher();

  const handleSubmit = () => {
    if (email === "") {
      alert("Email is required");
      return;
    }
    if (purpose === "") {
      alert("Purpose is required");
      return;
    }

    const formData = new FormData();
    formData.append("id", "user-impersonate");
    formData.append("type", "user-impersonate");
    formData.append("email", email);
    formData.append("ttlSeconds", ttlSeconds);
    formData.append("purpose", purpose);

    fetcher.submit(formData, {
      method: "post",
      action: "/settings",
      encType: "multipart/form-data",
    });
  };

  return (
    <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
      <DialogContent>
        <DialogHeader>User Impersonation</DialogHeader>
        <div className="space-y-4">
          <FormField name="Email" className="my-2">
            <FormLabel>Email Address</FormLabel>
            <Input
              value={email}
              onChange={(e) => {
                setEmail(e.target.value);
              }}
            />
          </FormField>
          <FormField name="Purpose">
            <FormLabel>Purpose</FormLabel>
            <Input
              type="text-box"
              value={purpose}
              onChange={(e) => {
                setPurpose(e.target.value);
              }}
            />
          </FormField>
          <FormField name="Time To Live">
            <FormLabel>{`Time To Live(in seconds)`}</FormLabel>
            <Input
              type="text-box"
              value={ttlSeconds}
              onChange={(e) => {
                setTtlSeconds(e.target.value);
              }}
            />
          </FormField>

          <Button onClick={handleSubmit}>Impersonate</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export const ImpersonationSection = ({
  section,
}: {
  section: SettingsSection;
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  return (
    <>
      <ImpersonationModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
      />
      <div
        className="mt-1 flex cursor-pointer items-center py-1 md:px-2"
        key={section.id}
        onClick={() => setIsModalOpen(true)}
      >
        {sidebarOptions[section.id as keyof typeof sidebarOptions]?.icon}
        <span className="ml-1 whitespace-nowrap">{section.title}</span>
      </div>
    </>
  );
};
