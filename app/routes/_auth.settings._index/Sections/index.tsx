import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "~/@shadcn/ui/tabs";
import { Typography } from "~/@ui/Typography";
import { ArrowBackOutlined } from "@mui/icons-material";
import { Button } from "~/@shadcn/ui/button";
import {
  Setting,
  SettingsSection,
  SectionLayout,
} from "~/api/openapi/generated";

import SettingsItem from "./components/SettingsItem";

const SettingsList = (props: {
  settings: Setting[];
  layout: SectionLayout;
  handleToggleChange: Function;
  handleSelectChange: Function;
  updatableSettingsState: { [id: string]: any };
}) => {
  const {
    settings,
    layout,
    handleSelectChange,
    handleToggleChange,
    updatableSettingsState,
  } = props;
  return (
    <div
      className={`${
        layout === SectionLayout.Grid &&
        "grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
      }`}
    >
      {settings.map((setting: Setting) => {
        return (
          <SettingsItem
            key={setting.id}
            setting={setting}
            handleSelectChange={handleSelectChange}
            handleToggleChange={handleToggleChange}
            updatableSettingsState={updatableSettingsState}
          />
        );
      })}
    </div>
  );
};

const TabsContentWrapper = (props: {
  settings: Setting[];
  option: string;
  layout: SectionLayout;
  handleToggleChange: Function;
  handleSelectChange: Function;
  updatableSettingsState: { [id: string]: any };
}) => {
  const {
    settings,
    option,
    handleSelectChange,
    handleToggleChange,
    updatableSettingsState,
    layout,
  } = props;
  return (
    <TabsContent value={option} className="space-y-4">
      <SettingsList
        layout={layout}
        handleSelectChange={handleSelectChange}
        handleToggleChange={handleToggleChange}
        updatableSettingsState={updatableSettingsState}
        settings={settings.filter(
          (setting: Setting) => option === "all" || setting.tabType === option
        )}
      />
    </TabsContent>
  );
};

const Section = (props: {
  subSection: SettingsSection;
  handleToggleChange: Function;
  handleSelectChange: Function;
  updatableSettingsState: { [id: string]: any };
}) => {
  const {
    subSection,
    handleSelectChange,
    handleToggleChange,
    updatableSettingsState,
  } = props;

  const { tabs, settings, layout } = subSection;
  return (
    <div className="space-y-6">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <Typography variant={"h3"}>{subSection.title}</Typography>
        {tabs && (
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">Show:</span>
            <div className="overflow-x-auto">
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                {tabs.map((tab: string) => (
                  <TabsTrigger key={tab} value={tab}>
                    {tab}
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>
          </div>
        )}
      </div>

      {tabs ? (
        <div>
          <TabsContentWrapper
            settings={settings || []}
            option="all"
            layout={layout || SectionLayout.Row}
            handleSelectChange={handleSelectChange}
            handleToggleChange={handleToggleChange}
            updatableSettingsState={updatableSettingsState}
          />
          {tabs.map((tab: string) => (
            <TabsContentWrapper
              key={tab}
              settings={settings || []}
              option={tab}
              layout={layout || SectionLayout.Row}
              handleSelectChange={handleSelectChange}
              handleToggleChange={handleToggleChange}
              updatableSettingsState={updatableSettingsState}
            />
          ))}
        </div>
      ) : (
        <div>
          <SettingsList
            settings={settings || []}
            layout={layout || SectionLayout.Row}
            handleSelectChange={handleSelectChange}
            handleToggleChange={handleToggleChange}
            updatableSettingsState={updatableSettingsState}
          />
        </div>
      )}
    </div>
  );
};

export default (props: {
  settingsSection: SettingsSection;
  handleToggleChange: Function;
  handleSelectChange: Function;
  updatableSettingsState: { [id: string]: any };
  onBackClick: () => void;
}) => {
  const {
    settingsSection,
    handleSelectChange,
    handleToggleChange,
    updatableSettingsState,
    onBackClick,
  } = props;
  return (
    <div className="space-y-6">
      <Typography className={"pb-2"} variant={"h2"}>
        <Button
          className="mr-2 md:hidden"
          size="icon-sm"
          variant="outline"
          onClick={onBackClick}
        >
          <ArrowBackOutlined className="text-gray-500" />
        </Button>
        {settingsSection.title}
      </Typography>
      {settingsSection.settings?.map((subSection: SettingsSection) => {
        return subSection.tabs ? (
          <Tabs defaultValue="all" className="w-full" key={subSection.id}>
            <Section
              subSection={subSection}
              handleSelectChange={handleSelectChange}
              handleToggleChange={handleToggleChange}
              updatableSettingsState={updatableSettingsState}
            />
          </Tabs>
        ) : (
          <div>
            <Section
              subSection={subSection}
              handleSelectChange={handleSelectChange}
              handleToggleChange={handleToggleChange}
              updatableSettingsState={updatableSettingsState}
            />
          </div>
        );
      })}
    </div>
  );
};
