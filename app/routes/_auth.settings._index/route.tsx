import { AuthorizationError } from "remix-auth";
import { ActionFunctionArgs, json, LoaderFunctionArgs } from "@remix-run/node";
import { Steps } from "intro.js-react";

import { ContentV2, HeaderV2, LayoutV2 } from "~/@ui/layout/LayoutV2";
import { Avatar, AvatarImage } from "~/@shadcn/ui/avatar";
import { Typography } from "~/@ui/Typography";
import { Button } from "~/@shadcn/ui/button";
import {
  INTERCOM_LAUNCHER_SELECTOR,
  useIntercom,
} from "~/third-party/Intercom/Intercom";
import {
  HeadsetMicOutlined,
  LinkOutlined,
  SettingsOutlined,
  SupervisedUserCircleOutlined,
} from "@mui/icons-material";
import { Suspense, useEffect, useRef, useState } from "react";
import {
  Configuration,
  SettingsApi,
  SettingsSection,
  SettingsSectionSettingsInner,
} from "~/api/openapi/generated";
import { SalesforceIcon } from "~/@ui/assets/SalesforceIcon";
import { WebexIcon } from "~/@ui/assets/WebexIcon";
import { GoogleMeetIcon } from "~/@ui/assets/GoogleMeetIcon";
import { MicrosoftTeamsIcon } from "~/@ui/assets/MicrosoftTeamsIcon";
import { OutlookIcon } from "~/@ui/assets/OutlookIcon";
import { configurationParameters } from "~/api/openapi/configParams";
import {
  authenticator,
  getUserSessionOrRedirect,
  IMPERSONATE_STRATEGY,
} from "~/auth/authenticator.server";
import {
  Link,
  NavLink,
  useFetcher,
  useLoaderData,
  useLocation,
  useNavigate,
} from "@remix-run/react";
import Sections from "./Sections";
import { Id, toast } from "react-toastify";
import { logError } from "~/utils/log.server";
import { useFlag } from "~/context/flags";
import { ZoomIcon } from "~/@ui/assets/ZoomIcon";
import { GoogleCalendarIcon } from "~/@ui/assets/GoogleCalendarIcon";
import { RedtailLogo } from "~/@ui/assets/RedtailLogo";
import { WealthboxIcon } from "~/@ui/assets/WealthboxIcon";

import * as all from "@mui/icons-material";
import { SettingsBanner } from "~/@ui/SettingsBanner";
import { Switch } from "~/@shadcn/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/@shadcn/ui/select";
import { ImpersonationSection } from "./Sections/components/UserImpersonation";
import { flattenZodErrors } from "~/utils/validation";
import { z } from "zod";
import { cn } from "~/@shadcn/utils";
import { tutorialSteps } from "./utils";
import useOnboarding from "~/utils/useOnboarding";

const UserProfile = ({
  firstName,
  lastName,
  email,
}: {
  firstName: string;
  lastName: string;
  email: string;
}) => {
  return (
    <>
      <div className="flex flex-row items-center p-2">
        <Avatar className="h-14 w-14">
          <AvatarImage src="https://avatars.githubusercontent.com/u/20711954?v=4" />
        </Avatar>
        <div className="ml-4 flex flex-col">
          <Typography variant="h2">
            {firstName} {lastName}
          </Typography>
          <Typography className="text-md" variant="body1">
            {email}
          </Typography>
        </div>
      </div>
    </>
  );
};

const ChatSupportButton = () => {
  return (
    <span
      className="fixed bottom-[70px] right-5 cursor-pointer rounded-full bg-primary p-3 text-primary-foreground md:hidden"
      id={INTERCOM_LAUNCHER_SELECTOR}
    >
      <HeadsetMicOutlined />
    </span>
  );
};

type IntegrationStatus = "Success" | "Failed" | "Retry required";

type SettingActionResponse = {
  success: boolean;
};

type Integration = {
  name: string | null;
  status: IntegrationStatus;
};

export const sidebarOptions = {
  integrations: {
    icon: <LinkOutlined fontSize="small" aria-label="Integrations" />,
    title: "Integrations",
  },
  settings: {
    icon: <SettingsOutlined fontSize="small" aria-label="Settings" />,
    title: "Settings",
  },
  user_impersonation: {
    icon: (
      <SupervisedUserCircleOutlined
        fontSize="small"
        aria-label="User impersonation"
      />
    ),
    title: "User Impersonation",
  },
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const queryParams = new URL(request.url).searchParams;
  const integrationStatus = queryParams.get("integrationStatus");
  let integration: Integration | null = null;
  if (integrationStatus !== null) {
    let status: IntegrationStatus;
    switch (integrationStatus) {
      case "true":
        status = "Success";
        break;
      case "false":
        status = "Failed";
        break;
      case "retryRequired":
        status = "Retry required";
        break;
      default:
        status = "Failed";
        break;
    }
    integration = {
      name: queryParams.get("integration"),
      status: status,
    };
  }
  const config = new Configuration(await configurationParameters(request));
  const settingsConfig = await new SettingsApi(config).settingsListSettings();
  const { firstName, lastName, email } = await getUserSessionOrRedirect(
    request
  );
  return {
    firstName,
    lastName,
    email,
    settingsConfig,
    integration: integration,
  };
};

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    // Clone the request to allow for multiple reads of the body since in the case of user impersonation we need to read the FormData again
    const cloned_request = request.clone();
    const formData = await request.formData();
    const id = formData.get("id") as string;
    const enabled = formData.get("enabled") === "true";
    const value = formData.get("value") as string;
    const type = formData.get("type") as string;

    if (!id) {
      throw new Error("Required parameter 'id' is missing or undefined.");
    }

    const config = new Configuration(await configurationParameters(request));
    switch (type) {
      case "toggle":
        await new SettingsApi(config).settingsUpdateToggleSetting({
          id,
          enabled,
        });
        break;
      case "select":
        await new SettingsApi(config).settingsUpdateSelectSetting({
          id,
          value: value ?? "",
        });
        break;
      case "user-impersonate":
        try {
          return authenticator.authenticate(
            IMPERSONATE_STRATEGY,
            cloned_request,
            {
              successRedirect: "/",
              throwOnError: true,
              context: { request: cloned_request },
            }
          );
        } catch (error) {
          // authenticator.authenticate redirects using thrown a Response, allow it to
          // bubble up
          if (error instanceof Response) throw error;

          // Auth service throws AuthorizationErrors
          if (error instanceof AuthorizationError) {
            if (error.cause instanceof z.ZodError) {
              return json(
                { errors: flattenZodErrors(error.cause) },
                { status: 400 }
              );
            }
            return json({ errors: [error.cause?.message] }, { status: 400 });
          }
        }
        break;
      default:
        throw new Error("Invalid type");
    }

    return { success: true };
  } catch (e) {
    logError("!!! api/settings.action", e);
    return { success: false };
  }
};

const NewSettingsRoute = () => {
  const { firstName, lastName, email, settingsConfig, integration } =
    useLoaderData<typeof loader>();

  const location = useLocation();

  const [currentOption, setCurrentOption] = useState("integrations");
  const toastId = useRef<Id | null>(null);
  const fetcher = useFetcher<SettingActionResponse>();

  const [isMenuVisible, setIsMenuVisible] = useState(true); // if true, show the settings menu; valid only for smaller devices

  const updateSettingsValue = (
    settingsSection: SettingsSection,
    action: { [id: string]: any }
  ): { [id: string]: any } => {
    settingsSection?.settings?.forEach((setting) => {
      const isSection = setting.kind === "SettingsSection";
      if (isSection) {
        return updateSettingsValue(setting, action);
      }
      if (setting.type === "toggle") {
        action[setting.id] = setting.enabled ?? false;
      }
      if (setting.type === "select") {
        action[setting.id] = setting.value ?? "";
      }
    });
    return action;
  };

  const [updatableSettingsState, setUpdatableSettingsState] = useState<{
    [id: string]: any;
  }>(() =>
    settingsConfig.reduce((action, settingSection) => {
      settingSection?.settings?.forEach((setting) => {
        return updateSettingsValue(setting, action);
      });
      return action;
    }, {} as { [id: string]: any })
  );

  const handleToggleChange = (id: string, checked: boolean) => {
    setUpdatableSettingsState((prev) => ({ ...prev, [id]: checked }));
    lastSubmission.current = { id, oldValue: !checked, newValue: checked };
    toastId.current && toast.dismiss(toastId.current);
    toastId.current = toast.loading("Updating setting...");

    const formData = new FormData();
    formData.append("id", id);
    formData.append("type", "toggle");
    formData.append("enabled", checked.toString());

    fetcher.submit(formData, {
      method: "post",
      encType: "application/x-www-form-urlencoded",
    });
  };

  const handleSelectChange = (
    id: string,
    selectedOption: string | undefined
  ) => {
    setUpdatableSettingsState((prev) => ({ ...prev, [id]: selectedOption }));
    lastSubmission.current = {
      id,
      oldValue: updatableSettingsState[id],
      newValue: selectedOption,
    };
    toastId.current && toast.dismiss(toastId.current);
    toastId.current = toast.loading("Updating setting...");

    const formData = new FormData();
    formData.append("id", id);
    formData.append("type", "select");
    formData.append("value", selectedOption ?? "");

    fetcher.submit(formData, {
      method: "post",
      encType: "application/x-www-form-urlencoded",
    });
  };

  const lastSubmission = useRef<{
    id: string;
    oldValue: any;
    newValue: any;
  } | null>(null);

  useEffect(() => {
    if (integration === null) {
      return;
    }
    switch (integration.status) {
      case "Success":
        toast.success(`${integration.name} integration successful`, {
          toastId: "success-toast",
        });
        break;
      case "Failed":
        toast.error(`${integration.name} integration failed`, {
          toastId: "failed-toast",
        });
        break;
      case "Retry required":
        toast.error(
          `${integration.name} integration failed. Please try again.`,
          { toastId: "retry-toast" }
        );
        break;
    }
  }, [integration]);

  useEffect(() => {
    setUpdatableSettingsState(
      settingsConfig.reduce((action, settingSection) => {
        settingSection?.settings?.forEach((setting) => {
          return updateSettingsValue(setting, action);
        });
        return action;
      }, {} as { [id: string]: any })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [settingsConfig, setUpdatableSettingsState]);

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (!fetcher.data.success && lastSubmission.current) {
        const { id, oldValue } = lastSubmission.current;
        setUpdatableSettingsState((prev) => ({
          ...prev,
          [id]: oldValue,
        }));

        toast.update(toastId.current!, {
          render: "Failed to update setting. Please try again.",
          type: "error",
          isLoading: false,
          autoClose: 2000,
        });
      } else {
        toast.dismiss(toastId.current!);
      }
    }
  }, [fetcher.state, fetcher.data]);

  // when any item from the settings menu is clicked on
  const onSelectOption = (sectionId: string) => {
    setIsMenuVisible(false);
    setCurrentOption(sectionId);
  };

  return (
    <LayoutV2 key={location.pathname}>
      <ContentV2
        className="justify-start overflow-hidden overscroll-none"
        header={
          <HeaderV2
            className="h-auto border-b border-gray-200 py-0 pl-2"
            left={
              <UserProfile
                firstName={firstName || ""}
                lastName={lastName || ""}
                email={email || ""}
              />
            }
          />
        }
        innerClassName="p-0"
      >
        <div className="flex w-full flex-grow flex-row">
          {/* for smaller devices, hide the menu panel if `isMenuVisible` = false */}
          <div
            className={cn(
              "h-full p-5 md:block md:border-r md:border-solid md:border-gray-200 md:p-2 md:pr-10",
              !isMenuVisible && "hidden"
            )}
          >
            <>
              {settingsConfig.map((section: SettingsSection) => {
                if (section.kind === "SettingsSection") {
                  return (
                    <div key={section.id}>
                      <Typography variant={"h3"}>{section.title}</Typography>
                      <div className="flex flex-col">
                        {section.kind === "SettingsSection" &&
                          section?.settings?.map(
                            (section: SettingsSectionSettingsInner) => {
                              if (section.id == "user_impersonation") {
                                return (
                                  <ImpersonationSection
                                    key={section.id}
                                    section={section}
                                  />
                                );
                              }
                              return (
                                <div
                                  className={cn(
                                    "mt-1 flex cursor-pointer items-center py-1 md:px-2",
                                    currentOption === section.id &&
                                      "md:text-primary"
                                  )}
                                  key={section.id}
                                  onClick={() => onSelectOption(section.id)}
                                >
                                  {
                                    sidebarOptions[
                                      section.id as keyof typeof sidebarOptions
                                    ]?.icon
                                  }
                                  <span className="ml-1">{section.title}</span>
                                </div>
                              );
                            }
                          )}
                      </div>
                    </div>
                  );
                }
                return null;
              })}
            </>
          </div>

          {/* for smaller devices, hide the details panel if `isMenuVisible` = true */}
          <div
            className={cn(
              "w-full p-5 pb-16 md:block",
              isMenuVisible && "hidden"
            )}
          >
            {settingsConfig.map((section: SettingsSection) => {
              return section.settings?.map(
                (settingSection: SettingsSectionSettingsInner) => {
                  if (currentOption === settingSection.id) {
                    return (
                      <Sections
                        key={settingSection.id}
                        settingsSection={settingSection}
                        handleSelectChange={handleSelectChange}
                        handleToggleChange={handleToggleChange}
                        updatableSettingsState={updatableSettingsState}
                        onBackClick={() => setIsMenuVisible(true)}
                      />
                    );
                  }
                  return null;
                }
              );
            })}
          </div>
        </div>

        <ChatSupportButton />
      </ContentV2>
    </LayoutV2>
  );
};

const OldSettingsRoute = () => {
  const { firstName, lastName, email, settingsConfig, integration } =
    useLoaderData<typeof loader>();
  const { isTutorialEnabled, completeTutorial } = useOnboarding("settings");

  const fetcher = useFetcher<SettingActionResponse>();
  const toastId = useRef<Id | null>(null);
  const location = useLocation();
  const { isIntercomAvailable } = useIntercom();
  const [updatableSettingsState, setUpdatableSettingsState] = useState<{
    [id: string]: any;
  }>(() =>
    settingsConfig.reduce((action, section) => {
      section?.settings?.forEach((setting) => {
        if (setting.type === "toggle") {
          action[setting.id] = setting.enabled ?? false;
        }
        if (setting.type === "select") {
          action[setting.id] = setting.value ?? "";
        }
      });
      return action;
    }, {} as { [id: string]: any })
  );
  const lastSubmission = useRef<{
    id: string;
    oldValue: any;
    newValue: any;
  } | null>(null);

  type DynamicIconProps = {
    iconName:
      | keyof typeof all
      | "ZoomIcon"
      | "GoogleCalendarIcon"
      | "RedtailLogo"
      | "WealthboxIcon"
      | "SalesforceIcon"
      | "WebexIcon"
      | "GoogleMeetIcon"
      | "MicrosoftTeamsIcon"
      | "OutlookIcon";
  };

  // Ensure that the state of the updatable settings is in sync with the settings config.
  //
  // It's possible for an action to succceed, but the state of, e.g., a toggle switch, not to
  // change.
  useEffect(() => {
    setUpdatableSettingsState(
      settingsConfig.reduce((action, section) => {
        section?.settings?.forEach((setting) => {
          if (setting.type === "toggle") {
            action[setting.id] = setting.enabled ?? false;
          }
          if (setting.type === "select") {
            action[setting.id] = setting.value ?? "";
          }
        });
        return action;
      }, {} as { [id: string]: any })
    );
  }, [settingsConfig, setUpdatableSettingsState]);

  useEffect(() => {
    if (integration === null) {
      return;
    }
    switch (integration.status) {
      case "Success":
        toast.success(`${integration.name} integration successful`, {
          toastId: "success-toast",
        });
        break;
      case "Failed":
        toast.error(`${integration.name} integration failed`, {
          toastId: "failed-toast",
        });
        break;
      case "Retry required":
        toast.error(
          `${integration.name} integration failed. Please try again.`,
          { toastId: "retry-toast" }
        );
        break;
    }
  }, [integration]);

  const DynamicIcon: React.FC<DynamicIconProps> = ({ iconName }) => {
    const muiIcon = all[iconName as keyof typeof all];

    if (muiIcon) {
      const IconComponent = muiIcon;
      return (
        <Suspense fallback={<div>Loading...</div>}>
          <IconComponent className="h-6 w-6" />
        </Suspense>
      );
    } else {
      const CustomIconMap = {
        ZoomIcon,
        GoogleCalendarIcon,
        RedtailLogo,
        WealthboxIcon,
        SalesforceIcon,
        WebexIcon,
        GoogleMeetIcon,
        MicrosoftTeamsIcon,
        OutlookIcon,
      };

      const CustomIconComponent =
        CustomIconMap[iconName as keyof typeof CustomIconMap];

      return (
        <Suspense fallback={<div>Loading...</div>}>
          <CustomIconComponent className="h-6 w-6" />
        </Suspense>
      );
    }
  };

  const handleToggleChange = (id: string, checked: boolean) => {
    setUpdatableSettingsState((prev) => ({ ...prev, [id]: checked }));
    lastSubmission.current = { id, oldValue: !checked, newValue: checked };
    toastId.current && toast.dismiss(toastId.current);
    toastId.current = toast.loading("Updating setting...");

    const formData = new FormData();
    formData.append("id", id);
    formData.append("type", "toggle");
    formData.append("enabled", checked.toString());

    fetcher.submit(formData, {
      method: "post",
      encType: "application/x-www-form-urlencoded",
    });
  };

  const handleSelectChange = (
    id: string,
    selectedOption: string | undefined
  ) => {
    setUpdatableSettingsState((prev) => ({ ...prev, [id]: selectedOption }));
    lastSubmission.current = {
      id,
      oldValue: updatableSettingsState[id],
      newValue: selectedOption,
    };
    toastId.current && toast.dismiss(toastId.current);
    toastId.current = toast.loading("Updating setting...");

    const formData = new FormData();
    formData.append("id", id);
    formData.append("type", "select");
    formData.append("value", selectedOption ?? "");

    fetcher.submit(formData, {
      method: "post",
      encType: "application/x-www-form-urlencoded",
    });
  };

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (!fetcher.data.success && lastSubmission.current) {
        const { id, oldValue } = lastSubmission.current;
        setUpdatableSettingsState((prev) => ({
          ...prev,
          [id]: oldValue,
        }));

        toast.update(toastId.current!, {
          render: "Failed to update setting. Please try again.",
          type: "error",
          isLoading: false,
          autoClose: 2000,
        });
      } else {
        toast.dismiss(toastId.current!);
      }
    }
  }, [fetcher.state, fetcher.data]);

  const onExitTutorial = (stepNumber: number) => {
    // check to avoid the issue (potentially with React wrapper itself) where onExitTutorial is called automatically at the beginning
    if (stepNumber >= 0) {
      completeTutorial();
    }
  };

  return (
    <LayoutV2 key={location.pathname}>
      <ContentV2 className="lg:max-w-full">
        <Steps
          enabled={isTutorialEnabled}
          steps={tutorialSteps}
          initialStep={0}
          onExit={onExitTutorial}
          onComplete={completeTutorial}
          options={{
            exitOnOverlayClick: false,
          }}
        />

        <div className="mx-auto w-full lg:max-w-[50rem]">
          <div className="flex w-full flex-col items-start pb-4">
            <SettingsBanner
              firstName={firstName}
              lastName={lastName}
              email={email}
            />
          </div>

          <div className="flex flex-col gap-3 self-stretch">
            {settingsConfig.map((settingSection) => (
              <div
                key={settingSection.id}
                data-onboarding={`settings-${settingSection.id}`}
              >
                <Typography variant="h3" color="primary">
                  {settingSection.title}
                </Typography>

                {settingSection.settings &&
                  settingSection.settings.map((config) => (
                    <div className="flex flex-col" key={config.id}>
                      {config.type === "routerLink" && (
                        <Typography
                          className="inline-flex items-center rounded-md px-2 py-2 hover:bg-accent"
                          asChild
                        >
                          <NavLink to={config.redirectPath!}>
                            <DynamicIcon
                              iconName={
                                config.icon as DynamicIconProps["iconName"]
                              }
                            />
                            <span className="mx-1 grow">{config.title}</span>
                            <all.ChevronRightOutlined className="h-6 w-6" />
                          </NavLink>
                        </Typography>
                      )}
                      {config.type === "link" && (
                        <Typography
                          className="inline-flex items-center rounded-md px-2 py-2 hover:bg-accent"
                          asChild
                        >
                          <a href={config.redirectPath ?? ""}>
                            <DynamicIcon
                              iconName={
                                config.icon as DynamicIconProps["iconName"]
                              }
                            />
                            <span className="mx-1 grow">{config.title}</span>
                            {config.value ? (
                              <Typography color="secondary" variant="body2">
                                {config.value}
                              </Typography>
                            ) : (
                              <all.LaunchOutlined className="h-6 w-6" />
                            )}
                          </a>
                        </Typography>
                      )}
                      {config.type === "info" && (
                        <Typography className="inline-flex items-center rounded-md px-2 py-2">
                          <DynamicIcon
                            iconName={
                              config.icon as DynamicIconProps["iconName"]
                            }
                          />
                          <span className="mx-1 grow">{config.title}</span>
                          <Typography asChild color="secondary" variant="body2">
                            <span>{config.value}</span>
                          </Typography>
                        </Typography>
                      )}
                      {isIntercomAvailable && config.type === "interCom" && (
                        <Typography
                          className="inline-flex items-center rounded-md px-2 py-2"
                          asChild
                        >
                          <Button
                            variant="ghost"
                            id={INTERCOM_LAUNCHER_SELECTOR}
                          >
                            <DynamicIcon iconName="HelpOutlineOutlined" />
                            <span className="grow text-left">
                              Chat with support
                            </span>
                            <Typography
                              asChild
                              color="secondary"
                              variant="body2"
                            >
                              <span>Open chat</span>
                            </Typography>
                          </Button>
                        </Typography>
                      )}
                      {config.type === "toggle" && (
                        <Typography className="inline-flex items-center rounded-md px-2 py-2">
                          <span className="mx-1 grow">{config.title}</span>
                          {config.value && (
                            <Typography
                              asChild
                              color="secondary"
                              variant="body2"
                            >
                              <span className="mx-2">{config.value}</span>
                            </Typography>
                          )}
                          <Switch
                            checked={updatableSettingsState[config.id]}
                            disabled={!(config.editable ?? true)}
                            onCheckedChange={(checked) =>
                              handleToggleChange(config.id, checked)
                            }
                          />
                        </Typography>
                      )}
                      {config.type === "select" && (
                        <Typography className="inline-flex items-center rounded-md px-2 py-2">
                          <span className="mx-1 grow">{config.title}</span>
                          <Select
                            value={updatableSettingsState[config.id]}
                            onValueChange={(nextValue) => {
                              if (
                                typeof nextValue !== "string" &&
                                nextValue !== undefined
                              ) {
                                return;
                              }
                              handleSelectChange(config.id, nextValue);
                            }}
                          >
                            <SelectTrigger className="w-auto rounded-md border-gray-300 text-sm shadow-sm">
                              <SelectValue placeholder="Select a time window" />
                            </SelectTrigger>
                            <SelectContent>
                              {config.options?.map((option) => (
                                <SelectItem
                                  className={"flex-basis:auto grow-0"}
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </Typography>
                      )}
                    </div>
                  ))}
              </div>
            ))}

            <Button className="mt-4" size="lg" variant="outline" asChild>
              <Link to="/auth/logout">
                <all.LogoutOutlined />
                Logout
              </Link>
            </Button>
            <Button
              className="text-destructive hover:bg-destructive-foreground hover:text-destructive"
              size="lg"
              variant="ghost"
              onClick={() => {
                if (window.confirm("Really delete your account?")) {
                  fetcher.submit(null, {
                    method: "delete",
                    action: "/auth/delete-user",
                  });
                }
              }}
            >
              <all.DeleteOutlined />
              Delete account
            </Button>
          </div>
        </div>
      </ContentV2>
    </LayoutV2>
  );
};

const Route = () => {
  const navigate = useNavigate();
  const isSettingsV2Enabled = useFlag("EnableSettingsV2");
  const isSettingsV3Enabled = useFlag("EnableSettingsV3");

  useEffect(() => {
    if (isSettingsV3Enabled) {
      navigate("/settings-new", { replace: true });
    }
  }, [isSettingsV3Enabled, navigate]);

  if (isSettingsV2Enabled) {
    return <NewSettingsRoute />;
  } else {
    return <OldSettingsRoute />;
  }
};

export default Route;
